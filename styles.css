/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #ff6b35;
    --text-primary: #000000;
    --text-secondary: #666666;
    --text-light: #999999;
    --background-primary: #ffffff;
    --background-secondary: #f8f8f8;
    --background-dark: #000000;
    
    /* Typography */
    --font-primary: 'Inter', sans-serif;
    --font-secondary: 'Playfair Display', serif;
    
    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 120px 0;
    --element-spacing: 2rem;
    
    /* Transitions */
    --transition-fast: 0.3s ease;
    --transition-medium: 0.6s ease;
    --transition-slow: 1s ease;
    
    /* Border Radius */
    --border-radius: 8px;
    --border-radius-large: 16px;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    overflow-x: hidden;
}

body.loading {
    overflow: hidden;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus styles for keyboard navigation */
a:focus,
button:focus,
input:focus,
textarea:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--secondary-color);
    padding: 8px;
    text-decoration: none;
    z-index: 10000;
    border-radius: 4px;
}

.skip-link:focus {
    top: 6px;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 2rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 3rem;
    text-align: center;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    font-family: var(--font-primary);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.btn-primary:hover {
    background-color: var(--accent-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

/* Loading Screen */
.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-medium), visibility var(--transition-medium);
}

.loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
}

.loader-text {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    display: flex;
    gap: 0.2rem;
}

.loader-letter {
    display: inline-block;
    animation: loaderBounce 1.4s infinite ease-in-out both;
}

.loader-letter:nth-child(1) { animation-delay: -0.32s; }
.loader-letter:nth-child(2) { animation-delay: -0.16s; }
.loader-letter:nth-child(3) { animation-delay: 0s; }
.loader-letter:nth-child(4) { animation-delay: 0.16s; }
.loader-letter:nth-child(5) { animation-delay: 0.32s; }
.loader-letter:nth-child(6) { animation-delay: 0.48s; }
.loader-letter:nth-child(7) { animation-delay: 0.64s; }

@keyframes loaderBounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.loader-progress {
    width: 200px;
    height: 2px;
    background-color: #eee;
    border-radius: 1px;
    overflow: hidden;
}

.loader-bar {
    width: 0%;
    height: 100%;
    background-color: var(--accent-color);
    animation: loaderProgress 2s ease-in-out;
}

@keyframes loaderProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

/* Navigation */
.navigation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all var(--transition-fast);
}

.navigation.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.scroll-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
    transform-origin: left;
    transform: scaleX(0);
    transition: transform 0.1s ease;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.logo-link {
    text-decoration: none;
    color: var(--text-primary);
}

.logo-text {
    font-family: var(--font-secondary);
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--accent-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: width var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
    padding: 10px;
    z-index: 1001;
}

.nav-toggle-line {
    width: 25px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all var(--transition-fast);
    transform-origin: center;
}

.nav-toggle.active .nav-toggle-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.nav-toggle.active .nav-toggle-line:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .nav-toggle-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero-video-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-video.loaded {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
    z-index: -1;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
}

.particle {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

.particle:nth-child(odd) {
    animation-direction: reverse;
    animation-duration: 8s;
}

.particle:nth-child(3n) {
    animation-duration: 10s;
    background-color: rgba(255, 107, 53, 0.1);
}

.hero-content {
    position: relative;
    text-align: center;
    color: var(--secondary-color);
    z-index: 1;
}

.hero-title {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 0.9;
}

.hero-title-line {
    display: block;
}

.hero-title-accent {
    color: var(--accent-color);
    font-style: italic;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.5rem);
    max-width: 600px;
    margin: 0 auto 3rem;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--secondary-color);
    opacity: 0.7;
}

.scroll-text {
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.scroll-line {
    width: 1px;
    height: 40px;
    background-color: var(--secondary-color);
    animation: scrollPulse 2s infinite;
}

@keyframes scrollPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

/* About Section */
.about {
    padding: var(--section-padding);
    background-color: var(--background-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    color: var(--text-secondary);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.about-image {
    position: relative;
}

.about-img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: var(--border-radius-large);
}

/* Skills Section */
.skills-section {
    margin-top: 5rem;
}

.skills-title {
    font-size: 2rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-primary);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.skill-item {
    background-color: var(--background-primary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.skill-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.skill-name {
    font-weight: 600;
    color: var(--text-primary);
}

.skill-percentage {
    font-weight: 700;
    color: var(--accent-color);
}

.skill-bar {
    width: 100%;
    height: 8px;
    background-color: #eee;
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
    border-radius: 4px;
    width: 0%;
    transition: width 1.5s ease-in-out;
}

/* Portfolio Section */
.portfolio {
    padding: var(--section-padding);
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    background-color: transparent;
    border: 2px solid var(--text-light);
    color: var(--text-secondary);
    border-radius: 30px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.portfolio-item {
    position: relative;
    border-radius: var(--border-radius-large);
    overflow: hidden;
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.portfolio-item:hover {
    transform: translateY(-10px);
}

.portfolio-image {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.8), rgba(255, 107, 53, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.portfolio-content {
    text-align: center;
    color: var(--secondary-color);
    padding: 2rem;
}

.portfolio-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.portfolio-description {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.portfolio-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.portfolio-tag {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    backdrop-filter: blur(10px);
}

.portfolio-link {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    background-color: var(--secondary-color);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.portfolio-link:hover {
    background-color: var(--accent-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Services Section */
.services {
    padding: var(--section-padding);
    background-color: var(--background-secondary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.service-item {
    text-align: center;
    padding: 3rem 2rem;
    background-color: var(--background-primary);
    border-radius: var(--border-radius-large);
    transition: transform var(--transition-fast);
}

.service-item:hover {
    transform: translateY(-10px);
}

.service-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 2rem;
    color: var(--accent-color);
}

.service-icon svg {
    width: 100%;
    height: 100%;
}

.service-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.service-description {
    color: var(--text-secondary);
}

/* Contact Section */
.contact {
    padding: var(--section-padding);
    background-color: var(--background-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: start;
}

.contact-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background-color: var(--background-primary);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform var(--transition-fast);
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
    flex-shrink: 0;
}

.contact-icon svg {
    width: 24px;
    height: 24px;
}

.contact-text {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.contact-label {
    font-size: 0.9rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 600;
}

.contact-link {
    font-size: 1.1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
    font-weight: 500;
}

.contact-link:hover {
    color: var(--accent-color);
}

.social-links {
    margin-top: 2rem;
}

.social-links h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icon {
    width: 45px;
    height: 45px;
    background-color: var(--background-primary);
    border: 2px solid #eee;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.social-icon:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--secondary-color);
    transform: translateY(-3px);
}

.social-icon svg {
    width: 20px;
    height: 20px;
}

.contact-form {
    background-color: var(--background-primary);
    padding: 3rem;
    border-radius: var(--border-radius-large);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1.2rem;
    border: 2px solid #eee;
    border-radius: var(--border-radius);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: all var(--transition-fast);
    background-color: var(--background-secondary);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    background-color: var(--background-primary);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.2);
}

.contact-form .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1.2rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.contact-form .btn svg {
    width: 18px;
    height: 18px;
    transition: transform var(--transition-fast);
}

.contact-form .btn:hover svg {
    transform: translateX(5px);
}

/* Footer */
.footer {
    padding: 3rem 0;
    background-color: var(--background-dark);
    color: var(--secondary-color);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-social {
    display: flex;
    gap: 2rem;
}

.social-link {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.social-link:hover {
    color: var(--accent-color);
}

/* Custom Cursor */
.cursor {
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
}

.cursor-dot {
    width: 8px;
    height: 8px;
    background-color: var(--accent-color);
    border-radius: 50%;
    position: absolute;
    transform: translate(-50%, -50%);
    transition: transform 0.1s ease;
}

.cursor-outline {
    width: 30px;
    height: 30px;
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    position: absolute;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    opacity: 0.5;
}

.cursor.hover .cursor-outline {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 1;
}

.cursor.click .cursor-dot {
    transform: translate(-50%, -50%) scale(1.5);
}

@media (max-width: 768px) {
    .cursor {
        display: none;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(255, 107, 53, 0.95));
        backdrop-filter: blur(20px);
        transition: left var(--transition-medium);
        z-index: 1000;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-list {
        flex-direction: column;
        padding: 6rem 2rem 2rem;
        gap: 2rem;
        height: 100%;
        justify-content: flex-start;
        align-items: center;
    }

    .nav-link {
        color: var(--secondary-color);
        font-size: 1.5rem;
        font-weight: 600;
        opacity: 0;
        transform: translateY(30px);
        transition: all var(--transition-fast);
    }

    .nav-menu.active .nav-link {
        opacity: 1;
        transform: translateY(0);
    }

    .nav-menu.active .nav-link:nth-child(1) { transition-delay: 0.1s; }
    .nav-menu.active .nav-link:nth-child(2) { transition-delay: 0.2s; }
    .nav-menu.active .nav-link:nth-child(3) { transition-delay: 0.3s; }
    .nav-menu.active .nav-link:nth-child(4) { transition-delay: 0.4s; }
    .nav-menu.active .nav-link:nth-child(5) { transition-delay: 0.5s; }

    .nav-link:hover {
        color: var(--accent-color);
    }

    .nav-link::after {
        background-color: var(--secondary-color);
    }

    .nav-toggle {
        display: flex;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .about-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 2rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .portfolio-filters {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-item {
        padding: 2rem 1.5rem;
    }

    .social-icons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    :root {
        --section-padding: 60px 0;
        --wrap-pad: 15px;
    }

    .container {
        padding: 0 1rem;
    }

    .hero-title {
        font-size: clamp(2.5rem, 12vw, 5rem);
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-cta .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .about-stats {
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .contact-item {
        padding: 1rem;
    }

    .contact-icon {
        width: 40px;
        height: 40px;
    }

    .contact-icon svg {
        width: 20px;
        height: 20px;
    }

    .social-icon {
        width: 40px;
        height: 40px;
    }

    .social-icon svg {
        width: 18px;
        height: 18px;
    }
}
