<!DOCTYPE html><!-- Last Published: Tue Jun 24 2025 12:54:19 GMT+0000 (Coordinated Universal Time) -->
<html data-wf-domain="www.thinreel.com" data-wf-page="681062e6060f2b85468c82cd" data-wf-site="681062e6060f2b85468c82cf"
    lang="en">

<head>
    <meta charset="utf-8" />
    <title>Video Production Company | Thin Reel | Bournemouth &amp; London</title>
    <meta
        content="The bold, creative video production company.  All in-house production, with studios in Bournemouth &amp; London. Working across the UK &amp; Europe."
        name="description" />
    <meta content="Thin Reel | Production Allies for Agencies | Content Production Company" property="og:title" />
    <meta
        content="A creative production company working across the UK &amp; Europe. Built to support agency teams from pitch to post. All in-house crew &amp; kit. This is creative content without the chaos."
        property="og:description" />
    <meta
        content="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/685a7181ac8c398a383cc008_Open%20Graph_THINREEL.jpg"
        property="og:image" />
    <meta content="Thin Reel | Production Allies for Agencies | Content Production Company" property="twitter:title" />
    <meta
        content="A creative production company working across the UK &amp; Europe. Built to support agency teams from pitch to post. All in-house crew &amp; kit. This is creative content without the chaos."
        property="twitter:description" />
    <meta property="og:type" content="website" />
    <meta content="summary_large_image" name="twitter:card" />
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <link
        href="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/css/thinreel-staging.webflow.shared.01c7f4f4e.min.css"
        rel="stylesheet" type="text/css" />
    <style>
        @media (min-width:992px) {
            html.w-mod-js:not(.w-mod-ix) [data-w-id="2a6b4d60-b7fa-6576-a985-4098feb688d9"] {
                display: grid;
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="50a637bd-f747-dad3-8492-2194dab96361"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 0, 0) scale3d(0.8, 0.8, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 0, 0) scale3d(0.8, 0.8, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 0, 0) scale3d(0.8, 0.8, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 0, 0) scale3d(0.8, 0.8, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="03f574f0-040f-0ef3-bf21-be3b32480e11"] {
                -webkit-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="6b9ecb40-44d5-67c3-df4f-b7bef6a7fb33"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="4dc99c1a-64a3-d94c-3b61-c47d000ea003"] {
                -webkit-transform: translate3d(0, 125%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 125%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 125%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 125%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="60c6bb1e-4f4c-80d6-1ed5-f7dc93542c3b"] {
                -webkit-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="6b05a69a-d045-1231-60af-2cbf76934840"] {
                opacity: 0;
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="67e74de2-1a7c-0feb-9b3f-aec015e0993e"] {
                opacity: 0;
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="857a7f5d-677e-ee05-5e32-d7e63edbb6d5"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 0, 0) scale3d(0.5, 0.5, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 0, 0) scale3d(0.5, 0.5, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 0, 0) scale3d(0.5, 0.5, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 0, 0) scale3d(0.5, 0.5, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="d44f12ff-43ad-53e0-c4fc-8af9750ddc0d"] {
                opacity: 0;
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="a90b46d8-e6ca-55f2-701c-bc7055e6c315"] {
                -webkit-transform: translate3d(0, 40%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 40%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 40%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 40%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                opacity: 0;
            }
        }

        @media (max-width:991px) and (min-width:768px) {
            html.w-mod-js:not(.w-mod-ix) [data-w-id="2a6b4d60-b7fa-6576-a985-4098feb688d9"] {
                display: grid;
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="4dc99c1a-64a3-d94c-3b61-c47d000ea003"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="60c6bb1e-4f4c-80d6-1ed5-f7dc93542c3b"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="03f574f0-040f-0ef3-bf21-be3b32480e11"] {
                -webkit-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="6b9ecb40-44d5-67c3-df4f-b7bef6a7fb33"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }
        }

        @media (max-width:767px) and (min-width:480px) {
            html.w-mod-js:not(.w-mod-ix) [data-w-id="2a6b4d60-b7fa-6576-a985-4098feb688d9"] {
                display: grid;
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="4dc99c1a-64a3-d94c-3b61-c47d000ea003"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="60c6bb1e-4f4c-80d6-1ed5-f7dc93542c3b"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="03f574f0-040f-0ef3-bf21-be3b32480e11"] {
                -webkit-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="6b9ecb40-44d5-67c3-df4f-b7bef6a7fb33"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }
        }

        @media (max-width:479px) {
            html.w-mod-js:not(.w-mod-ix) [data-w-id="2a6b4d60-b7fa-6576-a985-4098feb688d9"] {
                display: grid;
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="4dc99c1a-64a3-d94c-3b61-c47d000ea003"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 180%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="60c6bb1e-4f4c-80d6-1ed5-f7dc93542c3b"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="03f574f0-040f-0ef3-bf21-be3b32480e11"] {
                -webkit-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 120%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }

            html.w-mod-js:not(.w-mod-ix) [data-w-id="6b9ecb40-44d5-67c3-df4f-b7bef6a7fb33"] {
                opacity: 0;
                -webkit-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -moz-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                -ms-transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
                transform: translate3d(0, 0, 0) scale3d(0.7, 0.7, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            }
        }
    </style>
    <script
        type="text/javascript">!function (o, c) { var n = c.documentElement, t = " w-mod-"; n.className += t + "js", ("ontouchstart" in o || o.DocumentTouch && c instanceof DocumentTouch) && (n.className += t + "touch") }(window, document);</script>
    <link
        href="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6810639d47cc03409b9bfe9d_thinreel-favicon.png"
        rel="shortcut icon" type="image/x-icon" />
    <link
        href="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/681063a85a41acb914644ed3_thinreel-webclip.png"
        rel="apple-touch-icon" />
    <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-DSV7HMZXY9"></script>
    <script
        type="text/javascript">window.dataLayer = window.dataLayer || []; function gtag() { dataLayer.push(arguments); } gtag('js', new Date()); gtag('set', 'developer_id.dZGVlNj', true); gtag('config', 'G-DSV7HMZXY9');</script>
    <style>
        html {
            overflow-x: hidden;
        }
    </style>


    <style>
        [split-text] {
            opacity: 0;
        }

        html.w-editor [split-text] {
            opacity: 1;
        }

        .word {
            overflow: hidden;
            padding-bottom: 0.1em;
            margin-bottom: -0.1em;
            transform-origin: bottom;
        }
    </style>

    <style>
        .underline-link::before,
        .underline-link.is--alt::before,
        .underline-link.is--alt::after {
            content: "";
            position: absolute;
            bottom: 0em;
            left: 0;
            width: 100%;
            height: 0.0625em;
            background-color: #F45725;
            transition: transform 0.735s cubic-bezier(0.625, 0.05, 0, 1);
            transform-origin: right;
            transform: scaleX(0) rotate(0.001deg);
        }

        .underline-link:hover::before {
            transform-origin: left;
            transform: scaleX(1) rotate(0.001deg);
        }

        /* Alt */
        .underline-link.is--alt::before {
            transform-origin: left;
            transform: scaleX(1) rotate(0.001deg);
            transition-delay: 0.3s;
        }

        .underline-link.is--alt:hover::before {
            transform-origin: right;
            transform: scaleX(0) rotate(0.001deg);
            transition-delay: 0s;
        }

        .underline-link.is--alt::after {
            transform-origin: right;
            transform: scaleX(0) rotate(0.001deg);
            transition-delay: 0s;
        }

        .underline-link.is--alt:hover::after {
            transform-origin: left;
            transform: scaleX(1) rotate(0.001deg);
            transition-delay: 0.3s;
        }
    </style>

    <style>
        /* CSS Keyframe Animation */
        @keyframes translateX {
            to {
                transform: translateX(-100%);
            }
        }

        [data-css-marquee-list] {
            animation: translateX 30s linear;
            animation-iteration-count: infinite;
            animation-play-state: paused;
        }
    </style>


    <style>
        [data-modal-group-status] {
            transition: all 0.2s linear;
        }

        [data-modal-group-status="active"] {
            opacity: 1;
            visibility: visible;
        }

        [data-modal-name][data-modal-status="active"] {
            display: flex;
        }
    </style>
    <style>
        /* Animate Accordion Bottom Grid */
        .accordion-css__item-bottom {
            transition: grid-template-rows 0.6s cubic-bezier(0.625, 0.05, 0, 1);
        }

        [data-accordion-status="active"] .accordion-css__item-bottom {
            grid-template-rows: 1fr;
        }

        /* Animate Icon */
        .accordion-css__item-icon {
            transition: transform 0.6s cubic-bezier(0.625, 0.05, 0, 1);
        }

        [data-accordion-status="active"] .accordion-css__item-icon {
            transform: rotate(0.001deg);
        }
    </style>

    <style>
        body:has([data-cursor]:hover) .cursor {
            opacity: 1;
        }

        .button:hover .button-bg {
            transform: scale(0.95);
        }
    </style>
    <script
        src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf%2F66ba5a08efe71070f98dd10a%2F68489c7b36d75da54c685f49%2Fwlbtftz-1.1.1.js"
        type="text/javascript"></script>
</head>

<body class="overflow">
    <nav class="navigation-wrap">
        <div class="w-layout-grid _12-column-grid is-nav">
            <div id="w-node-_189aee36-6ba9-b43b-1663-31f825058482-25058480" class="nav-flex"><a href="/about"
                    class="underline-link is-nav">About Us</a>
                <link rel="prerender" href="/about" /><a href="/case-studies" class="underline-link is-nav">Our Work</a>
                <link rel="prefetch" href="/case-studies" /><a href="/our-world" class="underline-link is-nav">Our
                    World</a>
                <div id="menu-trigger" data-w-id="189aee36-6ba9-b43b-1663-31f825058489" class="menu-controls"><svg
                        xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 50 50" fill="none"
                        class="menu-icon is-close">
                        <path d="M34.0181 11.8784L36.1395 13.9997L13.9993 36.1393L11.8779 34.018L34.0181 11.8784Z"
                            fill="currentColor"></path>
                        <path d="M16.1201 11.8784L13.9987 13.9997L36.1389 36.1393L38.2603 34.018L16.1201 11.8784Z"
                            fill="currentColor"></path>
                    </svg><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 50 50" fill="none"
                        class="menu-icon is-open">
                        <path d="M46 17V20H4V17H46Z" fill="currentColor"></path>
                        <path d="M46 27V30H4V27H46Z" fill="currentColor"></path>
                    </svg></div>
            </div>
            <div id="w-node-_189aee36-6ba9-b43b-1663-31f825058490-25058480" class="logo-container"><a
                    aria-label="ThinReel logo - link to homepage" href="/" aria-current="page"
                    class="logo-link w-inline-block w--current"><svg xmlns="http://www.w3.org/2000/svg" width="100%"
                        viewBox="0 0 131 27" fill="none" class="logo-svg">
                        <g clip-path="url(#clip0_202_7126)">
                            <path
                                d="M11.9946 3.65328C9.10209 3.65328 4.90223 3.64597 4.43135 3.65328C5.12597 2.34302 5.64218 1.37495 6.37043 0.00326253C6.81352 0.00326253 25.4599 0.000337831 25.7714 0.00326253C25.087 1.29159 24.3778 2.62525 23.8309 3.65328C22.2252 3.65328 16.6288 3.65036 16.2661 3.65328C14.9003 6.22117 6.17593 22.6302 4.81449 25.1893H0.542969C2.34604 21.7996 10.8759 5.75614 11.9946 3.65328Z"
                                fill="currentColor"></path>
                            <path
                                d="M53.5913 6.07459H57.3729C56.9327 6.91691 50.4194 19.167 50.4194 19.167H47.3968C47.3968 19.167 45.4124 12.8189 44.9342 11.3317C44.3609 12.4095 41.5152 17.7631 40.7679 19.167H36.9717C38.5408 16.216 41.7667 10.1487 43.9339 6.07459H46.5998C47.3704 8.81211 48.2888 11.8333 49.0828 14.565C49.6342 13.5165 52.9873 7.20937 53.5913 6.07459Z"
                                fill="currentColor"></path>
                            <path
                                d="M21.8481 19.167C22.8249 17.3303 23.54 15.9849 24.4394 14.293H18.1717C17.2198 16.0844 16.3731 17.6754 15.5805 19.167H11.7988C12.6075 17.6476 18.0328 7.45359 18.7611 6.07459H22.5427C21.8861 7.30881 20.434 10.0419 19.95 10.9515C20.959 10.9515 24.5432 10.9369 26.2176 10.9515C26.747 9.95566 28.4871 6.64637 28.8103 6.07459H32.592C32.1299 6.94323 26.3653 17.7865 25.6297 19.167H21.8481Z"
                                fill="currentColor"></path>
                            <path
                                d="M85.3851 16.4514H93.1385C92.4 17.8406 92.071 18.4578 91.6937 19.167C88.6052 19.1597 83.2574 19.1714 80.1689 19.167C80.8519 17.8831 85.8034 8.56936 87.1312 6.07459C88.304 6.06289 97.3574 6.07459 98.6559 6.07459C97.9979 7.31174 97.8166 7.65247 97.2185 8.77847C95.2984 8.76824 89.4651 8.77847 89.4651 8.77847L88.1402 11.2703H92.1105L90.673 13.9742H86.7027L85.3866 16.45L85.3851 16.4514Z"
                                fill="currentColor"></path>
                            <path
                                d="M100.689 16.4514H108.442C107.704 17.8406 107.375 18.4578 106.997 19.167C103.909 19.1597 98.5611 19.1714 95.4727 19.167C96.1556 17.8831 101.107 8.56936 102.435 6.07459C103.608 6.06289 112.661 6.07459 113.96 6.07459C113.302 7.31174 113.12 7.65247 112.522 8.77847C110.602 8.76824 106.718 8.77847 104.769 8.77847C104.353 9.56083 103.671 10.8448 103.444 11.2703H107.413L105.975 13.9742H102.006C101.736 14.4846 100.876 16.1004 100.69 16.45L100.689 16.4514Z"
                                fill="currentColor"></path>
                            <path
                                d="M115.988 16.4514H122.254L120.811 19.167H110.771L117.734 6.07459H121.514L115.988 16.4514Z"
                                fill="currentColor"></path>
                            <path
                                d="M36.3753 6.07459C37.3332 6.08044 39.1567 6.07459 40.1526 6.07459C39.629 7.06022 33.6978 18.2135 33.1903 19.167H29.4131C29.6368 18.7093 35.9191 6.93007 36.3753 6.07459Z"
                                fill="currentColor"></path>
                            <path
                                d="M80.8803 2.13795C79.7118 0.745797 78.0053 0.00292397 76.0706 0.00292397C75.7606 0.00292397 64.3923 0.00292397 64.3923 0.00292397L62.4518 3.65294H66.2349L54.7832 25.1889H59.0547L64.8471 14.293H69.1054C69.1054 14.293 75.4286 26.2067 75.6421 26.5869C76.4245 26.2667 78.5493 25.3454 78.9412 25.2006C77.201 21.9264 73.1415 14.293 73.1415 14.293H74.2675C74.6741 14.293 75.0806 14.2696 75.4798 14.1994C78.7789 13.6262 81.5866 10.7409 82.1832 7.36292C82.524 5.43408 82.0472 3.53011 80.8788 2.13649L80.8803 2.13795ZM78.5917 6.73119C78.2451 8.69658 76.4801 10.9515 73.9721 10.9515C72.3738 10.9559 71.2273 10.9515 69.6304 10.9515H66.6268L70.5078 3.65148H76.2198C76.9919 3.68658 77.6514 3.96442 78.0857 4.4821C78.5668 5.05534 78.7467 5.85378 78.5932 6.72973L78.5917 6.73119Z"
                                fill="currentColor"></path>
                            <path d="M51.0009 25.1889H8.5957L10.5362 21.5404H52.9414L51.0009 25.1889Z"
                                fill="currentColor"></path>
                            <path d="M71.1192 25.1889H62.8379L64.7784 21.5404H69.1786L71.1192 25.1889Z"
                                fill="currentColor"></path>
                            <path d="M58.6697 3.65295H27.6123L29.5558 0L60.6102 0.0029247L58.6697 3.65295Z"
                                fill="currentColor"></path>
                            <path d="M80.7773 21.5404L82.7252 25.2006L117.608 25.1889L119.549 21.5404H80.7773Z"
                                fill="currentColor"></path>
                            <path
                                d="M83.4785 0.00731101C84.3574 1.06313 84.9877 2.31197 85.3372 3.65733L129.06 3.65441L131 0.00292397L83.4785 0.00584866V0.00731101Z"
                                fill="currentColor"></path>
                        </g>
                        <defs>
                            <clippath id="clip0_202_7126">
                                <rect width="130.458" height="26.5884" fill="currentColor"
                                    transform="translate(0.542969)"></rect>
                            </clippath>
                        </defs>
                    </svg></a></div>
            <div id="w-node-_189aee36-6ba9-b43b-1663-31f8250584a4-25058480">
                <div class="nav-hide-mob">
                    <div class="primary-button"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 20 20"
                            fill="none" class="button-arrow">
                            <g clip-path="url(#clip0_234_3928)">
                                <path
                                    d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                    fill="currentColor"></path>
                            </g>
                            <defs>
                                <clippath id="clip0_234_3928">
                                    <rect width="19.41" height="20" fill="currentColor"></rect>
                                </clippath>
                            </defs>
                        </svg><a href="/contact" class="underline-link">Contact Us</a></div>
                </div><a aria-label="Instagram - opens in new tab" href="https://www.instagram.com/thinreel/"
                    target="_blank" class="menu-controls is-insta w-inline-block"><svg
                        xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 50 50" fill="none"
                        class="menu-icon">
                        <path
                            d="M38.2537 19.5066C38.2278 18.4004 38.0206 17.3058 37.6403 16.2666C37.3047 15.3757 36.7766 14.5699 36.0937 13.9066C35.4305 13.2236 34.6246 12.6955 33.7337 12.36C32.6945 11.9797 31.5999 11.7726 30.4937 11.7466C29.0803 11.6666 28.627 11.6666 25.0003 11.6666C21.3737 11.6666 20.9203 11.6666 19.507 11.7466C18.4007 11.7726 17.3062 11.9797 16.267 12.36C15.3761 12.6955 14.5702 13.2236 13.907 13.9066C13.2236 14.5669 12.6992 15.3739 12.3737 16.2666C11.9819 17.3031 11.7699 18.3988 11.747 19.5066C11.667 20.92 11.667 21.3733 11.667 25C11.667 28.6266 11.667 29.08 11.747 30.4933C11.7699 31.601 11.9819 32.6969 12.3737 33.7333C12.6992 34.6261 13.2236 35.433 13.907 36.0933C14.5702 36.7762 15.3761 37.3044 16.267 37.64C17.3062 38.0202 18.4007 38.2274 19.507 38.2533C20.9203 38.3333 21.3737 38.3333 25.0003 38.3333C28.627 38.3333 29.0803 38.3333 30.4937 38.2533C31.5999 38.2274 32.6945 38.0202 33.7337 37.64C34.6182 37.292 35.4217 36.7656 36.0938 36.0934C36.7659 35.4213 37.2923 34.6178 37.6403 33.7333C38.0206 32.6941 38.2278 31.5996 38.2537 30.4933C38.2537 29.08 38.3337 28.6266 38.3337 25C38.3337 21.3733 38.3337 20.92 38.2537 19.5066ZM35.8537 30.3333C35.8439 31.1797 35.6907 32.0182 35.4003 32.8133C35.1773 33.3884 34.8367 33.9106 34.4005 34.3468C33.9643 34.783 33.4421 35.1236 32.867 35.3466C32.0719 35.637 31.2334 35.7902 30.387 35.8C29.0537 35.8666 28.5603 35.88 25.0537 35.88C21.547 35.88 21.0537 35.88 19.7203 35.8C18.8413 35.8197 17.9657 35.6842 17.1337 35.4C16.5586 35.1769 16.0363 34.8364 15.6001 34.4001C15.164 33.964 14.8234 33.4417 14.6003 32.8666C14.304 32.0548 14.1506 31.1976 14.147 30.3333C14.147 29 14.067 28.5066 14.067 25C14.067 21.4933 14.067 21 14.147 19.6666C14.1506 18.8023 14.304 17.9452 14.6003 17.1333C14.8234 16.5582 15.164 16.0359 15.6001 15.5998C16.0363 15.1636 16.5586 14.8231 17.1337 14.6C17.9456 14.3036 18.8027 14.1502 19.667 14.1466C21.0003 14.1466 21.4937 14.0666 25.0003 14.0666C28.507 14.0666 29.0003 14.0666 30.3337 14.1466C31.1801 14.1563 32.0186 14.3096 32.8137 14.6C33.3887 14.8231 33.911 15.1636 34.3471 15.5998C34.7834 16.0359 35.1239 16.5582 35.347 17.1333C35.6615 17.942 35.833 18.7992 35.8537 19.6666C35.9203 21 35.9337 21.4933 35.9337 25C35.9337 28.5066 35.9203 29 35.8537 30.3333Z"
                            fill="currentColor"></path>
                        <path
                            d="M24.9998 18.1466C23.6444 18.1466 22.3193 18.5486 21.1923 19.3016C20.0653 20.0547 19.1869 21.125 18.6682 22.3773C18.1494 23.6295 18.0137 25.0075 18.2782 26.337C18.5426 27.6663 19.1953 28.8875 20.1538 29.8459C21.1122 30.8045 22.3334 31.4571 23.6628 31.7215C24.9922 31.9861 26.3702 31.8503 27.6225 31.3315C28.8748 30.8129 29.9452 29.9345 30.6982 28.8074C31.4512 27.6805 31.8532 26.3554 31.8532 24.9999C31.8532 23.1823 31.1312 21.4392 29.8458 20.1539C28.5606 18.8686 26.8174 18.1466 24.9998 18.1466ZM24.9998 29.4399C24.1217 29.4399 23.2633 29.1795 22.5332 28.6917C21.8029 28.2038 21.2338 27.5103 20.8978 26.699C20.5617 25.8878 20.4738 24.995 20.6451 24.1337C20.8164 23.2725 21.2393 22.4813 21.8603 21.8604C22.4812 21.2394 23.2724 20.8166 24.1336 20.6453C24.9949 20.4739 25.8877 20.5619 26.6989 20.8979C27.5102 21.234 28.2037 21.8031 28.6916 22.5333C29.1794 23.2634 29.4398 24.1218 29.4398 24.9999C29.4398 25.583 29.325 26.1603 29.1018 26.699C28.8788 27.2378 28.5517 27.7271 28.1394 28.1395C27.727 28.5518 27.2377 28.8789 26.6989 29.1019C26.1602 29.3251 25.5829 29.4399 24.9998 29.4399Z"
                            fill="currentColor"></path>
                        <path
                            d="M32.1205 16.28C31.8041 16.28 31.4947 16.3739 31.2315 16.5497C30.9685 16.7255 30.7634 16.9754 30.6422 17.2677C30.5211 17.5601 30.4895 17.8818 30.5513 18.1922C30.613 18.5025 30.7653 18.7876 30.9891 19.0114C31.2129 19.2352 31.4979 19.3875 31.8083 19.4493C32.1187 19.511 32.4403 19.4793 32.7327 19.3582C33.0251 19.2371 33.275 19.0321 33.4509 18.7689C33.6266 18.5058 33.7205 18.1965 33.7205 17.88C33.7205 17.4557 33.5519 17.0487 33.2518 16.7487C32.9518 16.4486 32.5449 16.28 32.1205 16.28Z"
                            fill="currentColor"></path>
                    </svg></a>
            </div>
        </div>
        <div class="menu-wrapper">
            <div class="menu-flex"><a href="/about" class="mobile-link w-inline-block">
                    <div>About us</div>
                </a><a href="/case-studies" class="mobile-link w-inline-block">
                    <div>Our Work</div>
                </a><a href="/our-world" class="mobile-link w-inline-block">
                    <div>Our World</div>
                </a>
                <div class="contact-cta-mob">
                    <div class="primary-button"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 20 20"
                            fill="none" class="button-arrow">
                            <g clip-path="url(#clip0_234_3928)">
                                <path
                                    d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                    fill="currentColor"></path>
                            </g>
                            <defs>
                                <clippath id="clip0_234_3928">
                                    <rect width="19.41" height="20" fill="currentColor"></rect>
                                </clippath>
                            </defs>
                        </svg><a href="/contact" class="underline-link is-mobile">Get in touch</a></div>
                </div>
            </div>
            <div class="mobile-menu-base"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 30 28"
                    fill="none" class="tr-svg is-menu">
                    <g clip-path="url(#clip0_90_68)">
                        <path
                            d="M9.5175 3.03457C7.11351 3.03457 3.62299 3.02849 3.23165 3.03457C3.80895 1.9456 4.23797 1.13982 4.84322 -0.000192261C5.21147 -0.000192261 20.7086 -0.00140762 20.9674 -0.000192261C20.3986 1.07054 19.8092 2.17895 19.3546 3.03335C18.0202 3.03335 13.369 3.03092 13.0676 3.03335C11.9324 5.16753 4.68157 18.8051 3.55007 20.932H0C1.49854 18.116 8.58775 4.78226 9.5175 3.03457Z"
                            fill="currentColor"></path>
                        <path
                            d="M28.8258 6.82185C27.8548 5.66483 26.4376 5.04742 24.8297 5.04742C24.5721 5.04742 15.1238 5.04742 15.1238 5.04742L13.5111 8.08097H16.6552L7.1377 25.9796H10.6878L15.5018 16.9239H19.041C19.041 16.9239 24.2962 26.8255 24.4736 27.1415C25.1238 26.8753 26.8898 26.1096 27.2155 25.9893C25.7692 23.2681 22.3954 16.9239 22.3954 16.9239H23.3312C23.6691 16.9239 24.0069 16.9045 24.3387 16.8462C27.0806 16.3697 29.4141 13.9718 29.9099 11.1643C30.1931 9.56128 29.7969 7.97888 28.8258 6.82064V6.82185ZM26.9238 10.6393C26.6358 12.2728 25.1688 14.1468 23.0845 14.1468C21.7561 14.1505 20.8032 14.1468 19.4761 14.1468H16.9797L20.2053 8.07975H24.9525C25.5942 8.10892 26.1423 8.33984 26.5033 8.77008C26.9031 9.2465 27.0526 9.91009 26.925 10.6381L26.9238 10.6393Z"
                            fill="currentColor"></path>
                    </g>
                    <defs>
                        <clippath id="clip0_90_68">
                            <rect width="30" height="27.1427" fill="currentColor"></rect>
                        </clippath>
                    </defs>
                </svg></div>
        </div>
    </nav>
    <div class="collection-list-wrapper w-dyn-list">
        <div role="list" class="w-dyn-items">
            <div role="listitem" class="w-dyn-item">
                <div class="page-wrapper is-show">
                    <header data-w-id="ca035c06-67cc-2e55-6108-f538510433f0" class="hero-section">
                        <div class="hero-container"><svg data-w-id="6b9ecb40-44d5-67c3-df4f-b7bef6a7fb33"
                                xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 300 36" fill="none"
                                class="intro-subheading">
                                <path
                                    d="M1.92795 35.2152C1.66289 34.813 0.960726 34.9851 0.763096 34.5828C0.109755 33.2645 1.7373 28.5911 1.0142 27.5146C0.635218 26.952 -0.255279 27.1426 0.0702288 26.0429L1.46061 26.7311C1.9233 25.6941 2.19301 20.5767 2.85797 20.1814C3.36716 19.9652 3.84845 20.5604 4.33206 20.486C5.05515 20.3744 5.87823 18.9375 7.62434 18.9375C9.78665 18.9375 10.9841 20.1907 11.012 22.2577C11.0701 26.2521 7.19886 29.0794 3.33229 28.5772C3.14861 29.9164 2.78357 31.2347 2.62779 32.5809C2.52782 33.4412 3.0184 34.7619 2.16046 35.2152H1.92795ZM7.91265 20.1163C6.28743 20.4604 4.55527 23.1319 4.08793 24.6199C3.42761 26.7264 3.03468 28.1378 5.63177 26.8311C6.96868 26.1591 8.44509 24.8199 8.845 23.3528C9.05425 22.5832 9.35419 19.8117 7.91033 20.1163H7.91265Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M205.452 13.4648C205.207 13.2114 203.133 10.3236 202.815 10.5654L201.162 16.5036L199.671 16.0689C198.937 11.7861 202.155 8.39849 202.231 4.13433L202.457 3.9553C204.331 5.21083 205.212 2.54167 207.093 3.70885C208.558 4.61794 207.091 8.40314 206.27 9.49824C205.761 10.1772 205.073 10.0028 204.91 10.6747C205.003 11.077 208.251 14.0182 208.755 14.6808C208.953 14.9389 209.186 15.0947 209.093 15.4899C208.851 15.8131 208.735 15.8573 208.335 15.7992C207.756 15.7178 205.965 13.9926 205.456 13.4625L205.452 13.4648ZM206.068 4.83417C205.74 4.46681 204.354 6.53379 204.203 6.7756C203.701 7.58239 203.213 8.47289 203.047 9.41686C205.175 9.49824 205.956 6.54542 206.068 4.83417Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M17.8188 26.4648C17.5746 26.2114 15.5007 23.3236 15.1822 23.5654L13.529 29.5036L12.0387 29.0689C11.304 24.7861 14.5218 21.3985 14.5986 17.1343L14.8241 16.9553C16.6981 18.2108 17.5793 15.5417 19.4603 16.7088C20.925 17.6179 19.4579 21.4031 18.6372 22.4982C18.128 23.1772 17.4398 23.0028 17.277 23.6747C17.37 24.077 20.6181 27.0182 21.1227 27.6808C21.3203 27.9389 21.5528 28.0947 21.4598 28.4899C21.218 28.8131 21.1018 28.8573 20.7018 28.7992C20.1229 28.7178 18.3326 26.9926 17.8234 26.4625L17.8188 26.4648ZM18.4349 17.8342C18.1071 17.4668 16.7213 19.5338 16.5702 19.7756C16.068 20.5824 15.5797 21.4729 15.4147 22.4169C17.5421 22.4982 18.3233 19.5454 18.4349 17.8342Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M254.661 16.0429V22.5763C254.661 22.6716 255.121 23.0111 254.67 23.2738L253.298 23.3459C253.177 23.1041 253.091 22.8274 253.056 22.5577C252.861 21.0324 252.705 17.4588 252.812 15.9359C252.882 14.9571 252.905 13.0993 254.075 13.0389C255.428 12.9691 255.142 13.7876 255.623 14.5176C256.342 15.6081 257.444 17.0775 258.281 18.0866C258.56 18.4237 260.213 20.4767 260.701 20.1698C260.506 19.563 260.429 18.8725 260.464 18.2307C260.55 16.6404 260.848 13.3411 261.217 11.8531C261.489 10.7557 262.624 10.6441 263.163 11.5625C262.566 14.513 262.215 17.5076 262.317 20.5256C262.338 21.1115 262.852 22.2298 262.333 22.5972C261.245 23.3645 257.825 19.835 257.093 19.0306C256.237 18.0912 255.598 16.9124 254.654 16.0475L254.661 16.0429Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M98.8833 6.04286V12.5763C98.8833 12.6716 99.3436 13.0111 98.8926 13.2738L97.5208 13.3459C97.3999 13.1041 97.3138 12.8274 97.279 12.5577C97.0837 11.0324 96.9279 7.45882 97.0348 5.93591C97.1046 4.95706 97.1278 3.09934 98.2973 3.03889C99.6505 2.96913 99.3645 3.78755 99.8458 4.51762C100.564 5.60808 101.666 7.07751 102.503 8.08659C102.782 8.42372 104.435 10.4767 104.924 10.1698C104.728 9.563 104.652 8.87246 104.687 8.23074C104.773 6.6404 105.07 3.34114 105.44 1.85311C105.712 0.755678 106.847 0.644075 107.386 1.56247C106.788 4.51297 106.437 7.50765 106.54 10.5256C106.561 11.1115 107.074 12.2298 106.556 12.5972C105.468 13.3645 102.048 9.83503 101.315 9.03056C100.46 8.09124 99.8203 6.91243 98.8763 6.04751L98.8833 6.04286Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M224.845 4.51366L227.924 16.6086C227.424 16.6319 226.25 16.5575 225.952 16.1482C225.468 15.4856 225.357 12.4282 224.859 11.4935C224.317 10.4798 221.585 11.7492 220.535 11.6144C220.011 11.9073 218.549 15.8669 217.83 16.397C217.112 16.9271 216.575 16.2692 215.838 16.1459L222.794 4.61131C223.134 4.28812 224.499 4.2114 224.85 4.51133L224.845 4.51366ZM224.436 9.95894L223.625 6.75036C222.52 7.78966 222.004 9.1847 221.183 10.4147C222.285 10.3031 223.299 9.78921 224.438 9.95662L224.436 9.95894Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M123.705 1.51366L126.783 13.6086C126.283 13.6319 125.109 13.5575 124.811 13.1482C124.328 12.4856 124.216 9.42815 123.718 8.49348C123.177 7.47976 120.445 8.74924 119.394 8.61438C118.871 8.90734 117.408 12.8669 116.69 13.397C115.971 13.9271 115.434 13.2692 114.697 13.1459L121.654 1.61131C121.993 1.28812 123.358 1.2114 123.709 1.51133L123.705 1.51366ZM123.295 6.95894L122.484 3.75036C121.379 4.78966 120.863 6.1847 120.043 7.41465C121.145 7.30305 122.158 6.78921 123.298 6.95662L123.295 6.95894Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M35.6719 23.4227L35.5184 24.083L33.5352 23.6971L34.6512 12.6577C34.9534 11.9369 35.9927 12.102 36.6996 12.1369C38.3573 12.2206 42.0472 12.5577 42.3704 14.5665C42.8261 17.3961 39.0362 23.4343 35.6719 23.4227ZM37.8691 20.5326C38.9107 19.3306 40.8196 16.122 40.5243 14.5642C40.2732 13.2413 37.6505 13.2087 36.5717 13.2924L35.644 22.2346C36.5298 21.8533 37.2506 21.2441 37.8691 20.5326Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M54.4213 9.27884C54.4818 9.38579 54.7957 11.4272 54.8073 11.7155C54.8933 13.7476 53.396 18.6837 52.0451 20.2461C50.206 22.3712 47.8182 21.8318 46.4673 19.5323C44.8979 16.8585 45.2955 13.0454 45.986 10.1461C46.4766 9.56947 47.7601 10.1949 47.9275 10.7692C48.1321 11.4644 47.5601 12.4781 47.4996 13.2454C47.3601 15.0171 47.5345 18.1861 48.6854 19.637C50.034 21.3412 51.6964 16.9818 51.9824 16.0587C52.6985 13.736 52.845 11.1831 53.0798 8.77197L54.4213 9.28116V9.27884Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M197.866 3.7509C199.382 5.22964 197.412 9.79605 196.645 11.4747C195.927 13.0465 194.927 15.8203 192.764 15.3227C191.504 15.0344 191.007 14.4252 190.653 13.2697C189.995 11.1097 190.028 6.49447 191.223 4.52282C192.167 2.96503 196.573 2.49072 197.866 3.7509ZM192.585 14.2346C192.844 14.2648 193.048 14.2416 193.283 14.1207C194.415 13.5371 196.624 7.6756 196.768 6.32474C196.891 5.16221 196.88 3.30216 195.245 4.14151L195.373 5.05991C192.976 4.16476 192.553 5.44819 192.151 7.50122C191.707 9.7635 192.067 12.0211 192.585 14.2323V14.2346Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M31.1586 14.7509C32.6745 16.2296 30.7052 20.7961 29.9379 22.4747C29.2195 24.0465 28.2197 26.8203 26.0574 26.3227C24.7972 26.0344 24.2997 25.4252 23.9463 24.2697C23.2883 22.1097 23.3208 17.4945 24.5159 15.5228C25.4599 13.965 29.8659 13.4907 31.1586 14.7509ZM25.8784 25.2346C26.1365 25.2648 26.3411 25.2416 26.5759 25.1207C27.7082 24.5371 29.917 18.6756 30.0612 17.3247C30.1844 16.1622 30.1728 14.3022 28.5383 15.1415L28.6661 16.0599C26.269 15.1648 25.8458 16.4482 25.4436 18.5012C24.9995 20.7635 25.3599 23.0211 25.8784 25.2323V25.2346Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M94.6332 3.7509C96.1492 5.22964 94.1798 9.79605 93.4126 11.4747C92.6941 13.0465 91.6943 15.8203 89.532 15.3227C88.2719 15.0344 87.7743 14.4252 87.4209 13.2697C86.7629 11.1097 86.7954 6.49447 87.9905 4.52282C88.9345 2.96503 93.3405 2.49072 94.6332 3.7509ZM89.353 14.2346C89.6111 14.2648 89.8157 14.2416 90.0505 14.1207C91.1828 13.5371 93.3916 7.6756 93.5358 6.32474C93.659 5.16221 93.6474 3.30216 92.0129 4.14151L92.1407 5.05991C89.7436 4.16476 89.3204 5.44819 88.9182 7.50122C88.4741 9.7635 88.8345 12.0211 89.353 14.2323V14.2346Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M79.5835 7.83197C78.3001 7.825 76.9446 7.74362 75.7425 8.28536L74.4335 18.6877C74.1568 19.5805 72.8781 18.4621 72.6386 18.0064C71.6923 16.2068 74.0034 8.98055 73.5407 8.52019C72.4363 8.65272 68.2233 9.041 68.6697 7.0461C68.8162 6.39276 69.9276 6.94845 70.3902 6.9252C71.0157 6.89265 73.5616 6.64387 73.7941 6.29976C73.9267 6.10445 73.936 5.77662 74.015 5.54644C74.7311 5.37904 75.152 6.10445 75.7588 6.21838C77.2096 6.48809 79.3348 5.53482 79.5835 7.83197Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M155.937 2.31332L154.756 7.10527C156.004 6.73558 157.669 6.16129 158.948 6.5333C159.162 6.59608 159.578 6.63095 159.403 6.98901C159.043 7.73071 155.183 8.09109 154.295 8.60028L153.593 11.2322C155.39 11.3508 157.043 10.5161 158.806 11.2183C159.097 13.0574 157.681 12.0832 156.974 12.0251C156.144 11.9577 154.946 12.4994 153.709 12.3808C153.212 12.332 151.526 11.9716 151.547 11.402L154.035 1.4763L154.646 1.14847C156.792 1.31122 159.357 0.216118 161.261 1.48792C161.266 2.15522 156.66 2.20404 155.939 2.31332H155.937Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M245.15 10.3133L243.969 15.1053C245.217 14.7356 246.882 14.1613 248.161 14.5333C248.375 14.5961 248.791 14.631 248.616 14.989C248.256 15.7307 244.396 16.0911 243.508 16.6003L242.806 19.2322C244.603 19.3508 246.256 18.5161 248.019 19.2183C248.309 21.0574 246.893 20.0832 246.187 20.0251C245.357 19.9577 244.159 20.4994 242.922 20.3808C242.425 20.332 240.739 19.9716 240.76 19.402L243.248 9.4763L243.859 9.14847C246.005 9.31122 248.57 8.21612 250.474 9.48792C250.479 10.1552 245.873 10.204 245.152 10.3133H245.15Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M285.605 19.3133L284.424 24.1053C285.672 23.7356 287.337 23.1613 288.616 23.5333C288.83 23.5961 289.246 23.631 289.071 23.989C288.711 24.7307 284.851 25.0911 283.963 25.6003L283.261 28.2322C285.058 28.3508 286.711 27.5161 288.474 28.2183C288.764 30.0574 287.349 29.0832 286.642 29.0251C285.812 28.9577 284.614 29.4994 283.377 29.3808C282.88 29.332 281.194 28.9716 281.215 28.402L283.703 18.4763L284.314 18.1485C286.46 18.3112 289.025 17.2161 290.929 18.4879C290.934 19.1552 286.328 19.204 285.607 19.3133H285.605Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M237.457 8.28991C236.566 8.39919 236.08 7.67377 235.485 7.19946C232.73 8.8456 231.298 14.163 233.079 16.8508C234.06 18.3318 235.134 17.9947 236.064 16.6601C237.345 14.8257 236.845 14.0444 234.669 13.791C233.816 12.6308 238.617 13.4074 239.052 13.8259C239.261 14.1467 238.684 17.618 238.445 18.09C237.817 19.3316 236.999 17.9854 236.643 17.9854C236.557 17.9854 235.736 18.5969 235.094 18.6853C231.27 19.2107 230.342 14.9861 230.733 12.0937C230.954 10.4569 232.735 6.56704 234.609 6.26711C235.697 6.09273 237.766 6.9251 237.454 8.28991H237.457Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M181.657 6.7311C181.91 6.96128 187.309 5.49882 187.005 7.6495L181.183 8.21682C180.476 9.12591 179.878 14.5154 179.183 14.7409C178.748 14.8828 178.13 14.6014 177.807 14.2992L179.288 8.15637C178.86 6.67763 179.462 7.15426 179.75 6.57997C180.211 5.66158 180.206 3.762 180.732 2.73898C180.697 2.39487 179.455 2.65063 179.799 1.80663C179.948 1.46252 181.429 1.41602 181.748 1.46717C182.345 1.56482 182.578 1.97403 182.975 2.06936C184.619 2.45532 186.691 1.13934 187.928 2.7227C187.923 3.66203 183.377 3.26677 182.571 3.16446L181.655 6.73343L181.657 6.7311Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M65.3885 7.1648L65.4048 7.83209C60.8151 7.52053 59.1806 10.4152 59.5805 14.6073C59.8084 17.0021 62.8844 18.7017 65.0514 17.4625L65.4048 18.0321C63.4378 20.0619 57.8204 18.1739 57.3229 15.4583C56.3022 9.88511 59.5968 5.99762 65.3908 7.16247L65.3885 7.1648Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M273.945 15.1648L273.961 15.8321C269.372 15.5205 267.737 18.4152 268.137 22.6073C268.365 25.0021 271.441 26.7017 273.608 25.4625L273.961 26.0321C271.994 28.0619 266.377 26.1739 265.88 23.4583C264.859 17.8851 268.153 13.9976 273.947 15.1625L273.945 15.1648Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M300 21.1437C297.647 20.8926 295.892 22.4364 296.38 24.8266C296.985 27.7818 301.347 30.2928 296.903 32.6504C295.673 33.3038 294.576 33.72 293.272 33.0527C293.093 31.8018 294.981 31.9599 295.804 31.3344C298.038 29.6395 295.439 27.4353 294.685 25.5869C293.823 23.4757 294.132 21.4646 296.357 20.4183C297.489 19.8859 299.921 19.3883 300 21.146V21.1437Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M169.332 2.14371C166.979 1.89261 165.224 3.43645 165.712 5.82661C166.317 8.78176 170.679 11.2928 166.235 13.6504C165.005 14.3038 163.908 14.72 162.604 14.0527C162.425 12.8018 164.313 12.9599 165.136 12.3344C167.37 10.6395 164.771 8.43532 164.017 6.5869C163.155 4.47575 163.464 2.46457 165.689 1.41829C166.821 0.885856 169.253 0.388294 169.332 2.14604V2.14371Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M133.526 1.23237L130.738 12.0044C132.087 11.8881 133.947 11.5417 135.27 11.7765C135.695 11.8532 136.002 12.095 135.844 12.5694C135.565 13.411 130.21 13.1111 129.111 13.6087L129.571 12.5763C129.559 12.3926 128.992 12.3113 128.918 12.1927C128.743 11.9114 130.108 6.58233 130.338 5.76391C130.624 4.74785 131.25 2.14379 131.708 1.38349C132.166 0.6232 132.826 1.22772 133.528 1.23004L133.526 1.23237Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M141.897 0.232365L139.109 11.0044C140.458 10.8881 142.318 10.5417 143.641 10.7765C144.066 10.8532 144.373 11.095 144.215 11.5694C143.936 12.411 138.582 12.1111 137.482 12.6087L137.942 11.5763C137.931 11.3926 137.363 11.3113 137.289 11.1927C137.114 10.9114 138.479 5.58233 138.709 4.76391C138.995 3.74785 139.621 1.14379 140.079 0.383494C140.537 -0.3768 141.197 0.227715 141.899 0.23004L141.897 0.232365Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M82.6379 6.86451C83.454 6.67618 84.2422 6.93426 84.2561 7.86429L83.268 16.081C83.0587 16.7553 82.1659 16.388 81.7916 16.0066C81.6311 12.9864 82.1961 9.86384 82.6379 6.86451Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M147.275 1.86451C148.091 1.67618 148.879 1.93426 148.893 2.86429L147.905 11.081C147.695 11.7553 146.803 11.388 146.428 11.0066C146.268 7.98639 146.833 4.86384 147.275 1.86451Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M277.943 16.8645C278.759 16.6762 279.547 16.9343 279.561 17.8643L278.573 26.081C278.363 26.7553 277.471 26.388 277.096 26.0066C276.936 22.9864 277.501 19.8638 277.943 16.8645Z"
                                    fill="currentColor"></path>
                            </svg>
                            <div class="hero-title-div">
                                <div data-w-id="50a637bd-f747-dad3-8492-2194dab96361" class="hero-lottie"
                                    data-animation-type="lottie"
                                    data-src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/682fff9a9665db68cf77eeec_Clappy_Reworked_01.json"
                                    data-loop="1" data-direction="1" data-autoplay="1" data-is-ix2-target="0"
                                    data-renderer="svg" data-default-duration="0" data-duration="2"></div>
                                <div class="type-mask">
                                    <div class="move-target">
                                        <h1 data-w-id="60c6bb1e-4f4c-80d6-1ed5-f7dc93542c3b"
                                            class="display is-hero italic">Creative<span class="span-wrapper is-hero">
                                            </span>Content</h1>
                                    </div>
                                </div>
                            </div>
                            <div class="type-mask is-2">
                                <h2 data-w-id="4dc99c1a-64a3-d94c-3b61-c47d000ea003" class="display is-hero">Without the
                                    Chaos</h2>
                            </div>
                        </div>
                        <div class="video-parallax w-embed w-iframe">
                            <div data-video-id="33416" style="width: 100%; height: 100%;overflow: hidden;">

                                <iframe width="100%" height="100%"
                                    src="https://app.vidzflow.com/v/8JliJ9Z3AP?dq=576&ap=true&muted=true&loop=true&ctp=false&bv=true&piv=false&playsinline=false&bc=%234E5FFD&controls=false"
                                    title="ThinReel Showreel Snapshot" style="overflow: hidden;" frameborder="0"
                                    scrolling="no" allow="fullscreen"></iframe>
                            </div>
                        </div>
                        <div class="hero-overlay"></div>
                    </header>
                    <div class="content-wrapper">
                        <div data-w-id="03f574f0-040f-0ef3-bf21-be3b32480e11" class="show-reel-button-wrap">
                            <div data-w-id="73db9bf4-f8d7-0de3-3ac8-5ed00c2f89d6" class="showreel-trigger"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 9 35" fill="none"
                                    class="showreel-edge">
                                    <path d="M4.99971 0L9 0V35H0L4.99971 0Z" fill="currentColor"></path>
                                </svg>
                                <div class="showreel-flex"><svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                        viewBox="0 0 27 30" fill="none" class="showreel-icon">
                                        <path
                                            d="M25.7126 14.0804C26.3104 14.5074 26.2592 15.4116 25.617 15.7683L1.89031 28.9498C1.14517 29.3638 0.256918 28.7154 0.424089 27.8796L5.69669 1.51655C5.839 0.804985 6.66802 0.477154 7.25851 0.898934L25.7126 14.0804Z"
                                            fill="currentColor"></path>
                                    </svg>
                                    <div class="showreel-text">Showreel</div>
                                </div><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 9 35"
                                    fill="none" class="showreel-edge is-right">
                                    <path d="M4.00029 35L0 35L3.0598e-06 -7.86805e-07L9 0L4.00029 35Z"
                                        fill="currentColor"></path>
                                </svg>
                            </div>
                        </div>
                        <section data-w-id="c7abaa32-94ae-ed40-fa9a-548c827e9f2a" class="section dark-bg">
                            <div class="intro-container"><svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                    viewBox="0 0 30 28" fill="none" class="tr-svg">
                                    <g clip-path="url(#clip0_90_68)">
                                        <path
                                            d="M9.5175 3.03457C7.11351 3.03457 3.62299 3.02849 3.23165 3.03457C3.80895 1.9456 4.23797 1.13982 4.84322 -0.000192261C5.21147 -0.000192261 20.7086 -0.00140762 20.9674 -0.000192261C20.3986 1.07054 19.8092 2.17895 19.3546 3.03335C18.0202 3.03335 13.369 3.03092 13.0676 3.03335C11.9324 5.16753 4.68157 18.8051 3.55007 20.932H0C1.49854 18.116 8.58775 4.78226 9.5175 3.03457Z"
                                            fill="currentColor"></path>
                                        <path
                                            d="M28.8258 6.82185C27.8548 5.66483 26.4376 5.04742 24.8297 5.04742C24.5721 5.04742 15.1238 5.04742 15.1238 5.04742L13.5111 8.08097H16.6552L7.1377 25.9796H10.6878L15.5018 16.9239H19.041C19.041 16.9239 24.2962 26.8255 24.4736 27.1415C25.1238 26.8753 26.8898 26.1096 27.2155 25.9893C25.7692 23.2681 22.3954 16.9239 22.3954 16.9239H23.3312C23.6691 16.9239 24.0069 16.9045 24.3387 16.8462C27.0806 16.3697 29.4141 13.9718 29.9099 11.1643C30.1931 9.56128 29.7969 7.97888 28.8258 6.82064V6.82185ZM26.9238 10.6393C26.6358 12.2728 25.1688 14.1468 23.0845 14.1468C21.7561 14.1505 20.8032 14.1468 19.4761 14.1468H16.9797L20.2053 8.07975H24.9525C25.5942 8.10892 26.1423 8.33984 26.5033 8.77008C26.9031 9.2465 27.0526 9.91009 26.925 10.6381L26.9238 10.6393Z"
                                            fill="currentColor"></path>
                                    </g>
                                    <defs>
                                        <clippath id="clip0_90_68">
                                            <rect width="30" height="27.1427" fill="currentColor"></rect>
                                        </clippath>
                                    </defs>
                                </svg>
                                <div data-w-id="6013220c-b826-656a-6332-2ce2b99fca5f" class="intro-content-div">
                                    <div class="restricted-heading is-1">
                                        <h3 split-text="" class="heading-2 centre-align">You don&#x27;t need more
                                            pressures + Problems</h3>
                                    </div>
                                    <div class="centre-image-flex">
                                        <div class="intro-text">
                                            <p data-w-id="abdfb414-dc3a-23be-30c0-a113b8fe8d6d" style="opacity:0"
                                                class="centre-align">You’re constantly pushed to deliver more, faster
                                                and better. But production can leave you with headaches, from unclear
                                                costs and missed deadlines to creative clashes. </p>
                                            <div data-is-ix2-target="1" class="home-arrow is-1"
                                                data-w-id="34caa0c9-57a7-c0a9-893b-80cfa7391d06"
                                                data-animation-type="lottie"
                                                data-src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/681bfa4da5cf4b79f6bbe1ce_Arrow_Lottie_01.json"
                                                data-loop="0" data-direction="1" data-autoplay="0" data-renderer="svg"
                                                data-default-duration="0" data-duration="0.9166666666666666"
                                                data-ix2-initial-state="0"></div>
                                        </div>
                                        <div class="div-block-2">
                                            <div style="-webkit-transform:translate3d(0, 25%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);-moz-transform:translate3d(0, 25%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);-ms-transform:translate3d(0, 25%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);transform:translate3d(0, 25%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0)"
                                                class="img-div is-intro is-portrait is-left"><img
                                                    src="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681326067590af4387485810_DSCF8977.jpg"
                                                    loading="lazy" alt="" sizes="100vw"
                                                    srcset="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681326067590af4387485810_DSCF8977-p-500.jpg 500w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681326067590af4387485810_DSCF8977-p-800.jpg 800w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681326067590af4387485810_DSCF8977.jpg 1086w"
                                                    class="img-fill" /><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/681326c186efb709f7a0f5a0_intro-icon-left.svg"
                                                    loading="lazy" alt="" class="iconography-overlay" /></div>
                                            <div style="-webkit-transform:translate3d(0, 25%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);-moz-transform:translate3d(0, 25%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);-ms-transform:translate3d(0, 25%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);transform:translate3d(0, 25%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0)"
                                                class="img-div is-intro is-portrait is-right"><img
                                                    src="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6813260cdbb49642914d7fa1_DSCF3365.jpg"
                                                    loading="lazy" alt="" sizes="100vw"
                                                    srcset="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6813260cdbb49642914d7fa1_DSCF3365-p-500.jpg 500w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6813260cdbb49642914d7fa1_DSCF3365-p-800.jpg 800w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6813260cdbb49642914d7fa1_DSCF3365.jpg 1086w"
                                                    class="img-fill" /><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/681326c1402206ba9d5a3090_intro-icon-right.svg"
                                                    loading="lazy" alt="" class="iconography-overlay" /></div>
                                        </div>
                                        <div class="intro-text no-mobile">
                                            <p data-w-id="66a307ac-c4f3-8e8b-fa25-477f10523b93" style="opacity:0"
                                                class="centre-align">We work alongside agencies that need incredible
                                                content without the dramas. With crew, kit and post in-house, we help
                                                you get to great outcomes faster by working leaner.</p>
                                            <div data-is-ix2-target="1" class="home-arrow is-2"
                                                data-w-id="2ad7ebf5-f714-6880-3224-92c3b5cd137d"
                                                data-animation-type="lottie"
                                                data-src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/681bfa4da5cf4b79f6bbe1ce_Arrow_Lottie_01.json"
                                                data-loop="0" data-direction="1" data-autoplay="0" data-renderer="svg"
                                                data-default-duration="0" data-duration="0.9166666666666666"
                                                data-ix2-initial-state="0"></div>
                                        </div>
                                    </div>
                                    <div class="restricted-heading is-2">
                                        <h4 class="heading-2 centre-align is-inline">Just agile production <span
                                                class="italic orange">that&#x27;s ready to roll</span></h4>
                                        <div class="heading-2 centre-align is-roll">that&#x27;s ready<br />to roll</div>
                                        <div class="intro-text no-desktop">
                                            <p class="centre-align">We work alongside agencies that need incredible
                                                content without the dramas. With crew, kit and post in-house, we help
                                                you get to great outcomes faster by working leaner.</p>
                                        </div>
                                        <div class="subheading no-mobile">Wildly creative video, motion, and stills.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <div class="hero-illustration is-tall">
                            <div class="code-embed-2 w-embed">
                                <div style="width: 100%; height: 100%;"
                                    class="w-background-video w-background-video-atom">
                                    <video playsinline loop muted autoplay="" loop="" muted="" playsinline=""
                                        data-wf-ignore="true" data-object-fit="cover">
                                        <source
                                            src="https://player.vimeo.com/progressive_redirect/playback/1087855770/rendition/2160p/file.mp4?loc=external&signature=2b49f89bc9e84dd5c8404674e752aa71d15dd6f29af6fa067f200b8d84e558fb"
                                            data-wf-ignore="true" />
                                    </video>
                                </div>
                            </div>
                            <div class="scene-gradient is-home"></div>
                            <div class="scene-gradient is-bottom"></div>
                        </div>
                        <section class="section dark-bg is-kit">
                            <div data-w-id="22a51794-5f81-0660-c689-c441259dc9ef" class="side-padding">
                                <div class="display-heading">
                                    <div class="flex-even">
                                        <div split-text="" class="display is-kit is-nm">Kit.</div>
                                        <div class="img-div is-portrait is-crew">
                                            <div class="video-link w-embed w-iframe">
                                                <div data-video-id="33416"
                                                    style="width: 100%; height: 100%;overflow: hidden;">

                                                    <iframe width="100%" height="100%"
                                                        src="https://app.vidzflow.com/v/0nSlMwfq3g?dq=576&ap=true&muted=true&loop=true&ctp=false&bv=true&piv=false&playsinline=false&bc=%234E5FFD&controls=false"
                                                        title="Behind The Scenes" style="overflow: hidden;"
                                                        frameborder="0" scrolling="no" allow="fullscreen"></iframe>
                                                </div>
                                            </div><img
                                                src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/681326c186efb709f7a0f5a0_intro-icon-left.svg"
                                                loading="lazy" alt="" class="iconography-overlay" />
                                        </div>
                                        <div split-text="" class="display is-kit is-nm">Crew.</div>
                                    </div>
                                    <div class="flex-even is-top">
                                        <div class="display is-kit is-nd">Kit. Crew.</div>
                                        <div split-text="" class="display is-kit">Creativity</div>
                                        <div data-w-id="d44f12ff-43ad-53e0-c4fc-8af9750ddc0d"
                                            class="slanted-sub is-crew">It&#x27;s all handled.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="services-container">
                                <div class="service-block">
                                    <div data-css-marquee="" class="marquee-css is-service">
                                        <div data-css-marquee-list="" class="marquee-css__list">
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Brief Analysis</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Budgeting</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Creative Concepts</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Timelines</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Pitch Decks</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-layout-grid _12-column-grid is-service">
                                        <div id="w-node-_17ca26f9-0c91-3eb4-f87f-729a3b08358a-468c82cd"
                                            class="service-div is-number">
                                            <div class="slanted-container is-1">
                                                <div class="body-large">(01)</div><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6817fb1b8e783498a0cbb93b_service-divider-l.svg"
                                                    loading="lazy" alt="" class="slant" />
                                            </div>
                                            <h2 class="heading-3 italic is-service">Pitch In™</h2>
                                        </div>
                                        <div id="w-node-_27b6734b-0af6-2e26-7b1a-8b1f3e42b271-468c82cd"
                                            class="service-div is-text">
                                            <div class="body-large is-service">When you’re pitching, we can help you
                                                with scoping, pricing and creative feasibility so you know what’s
                                                possible before clients approve it.</div>
                                        </div>
                                        <div id="w-node-_8af4f85c-ed4b-824a-700f-7cd819cd226c-468c82cd"
                                            class="service-div is-right">
                                            <div class="slanted-container is-right"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6817fb1b8e783498a0cbb93b_service-divider-l.svg"
                                                    loading="lazy" alt="" class="slant" />
                                                <div class="heading-5 italic">+</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="service-block">
                                    <div data-css-marquee="" class="marquee-css is-service">
                                        <div data-css-marquee-list="" class="marquee-css__list">
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">location management</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">casting</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Scripting</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Storyboarding</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Permits + Legal</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-layout-grid _12-column-grid is-service">
                                        <div id="w-node-ba4f8dcc-1ddc-b0e2-a588-e38a3219fec2-468c82cd"
                                            class="service-div is-number">
                                            <div class="slanted-container is-2">
                                                <div class="body-large">(02)</div><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6817fb1b8e783498a0cbb93b_service-divider-l.svg"
                                                    loading="lazy" alt="" class="slant" />
                                            </div>
                                            <h2 class="heading-3 italic is-service">Pre</h2>
                                        </div>
                                        <div id="w-node-ba4f8dcc-1ddc-b0e2-a588-e38a3219feca-468c82cd"
                                            class="service-div is-text">
                                            <div class="body-large is-service">Detailed planning, creative input and
                                                seamless coordination to make every shoot run smoothly and deliver
                                                exactly what your client expects.</div>
                                        </div>
                                        <div id="w-node-ba4f8dcc-1ddc-b0e2-a588-e38a3219fecc-468c82cd"
                                            class="service-div is-right">
                                            <div class="slanted-container is-right"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6817fb1b8e783498a0cbb93b_service-divider-l.svg"
                                                    loading="lazy" alt="" class="slant" />
                                                <div class="heading-5 italic">+</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="service-block">
                                    <div data-css-marquee="" class="marquee-css is-service">
                                        <div data-css-marquee-list="" class="marquee-css__list">
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Crew + Kit</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">On-Location</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Studio</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">International</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Aerial Filming</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-layout-grid _12-column-grid is-service">
                                        <div id="w-node-_1026bc6d-c97c-0d8c-4bf4-11ecf7668721-468c82cd"
                                            class="service-div is-number">
                                            <div class="slanted-container is-3">
                                                <div class="body-large">(03)</div><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6817fb1b8e783498a0cbb93b_service-divider-l.svg"
                                                    loading="lazy" alt="" class="slant" />
                                            </div>
                                            <h2 class="heading-3 italic is-service">Production</h2>
                                        </div>
                                        <div id="w-node-_1026bc6d-c97c-0d8c-4bf4-11ecf7668729-468c82cd"
                                            class="service-div is-text">
                                            <div class="body-large is-service">Agile, scalable shoots that flex around
                                                your clients&#x27; demands. Fast, efficient and creative, whether it’s a
                                                big campaign or a quick-turnaround.</div>
                                        </div>
                                        <div id="w-node-_1026bc6d-c97c-0d8c-4bf4-11ecf766872b-468c82cd"
                                            class="service-div is-right">
                                            <div class="slanted-container is-right"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6817fb1b8e783498a0cbb93b_service-divider-l.svg"
                                                    loading="lazy" alt="" class="slant" />
                                                <div class="heading-5 italic">+</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="service-block">
                                    <div data-css-marquee="" class="marquee-css is-service">
                                        <div data-css-marquee-list="" class="marquee-css__list">
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Editing</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Motion + Animation</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Colour Grading</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Sound Design + Music</p>
                                            </div>
                                            <div class="marquee-css__item">
                                                <p class="marquee-css__item-p">Localisation</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-layout-grid _12-column-grid is-service">
                                        <div id="w-node-b25338df-8c5e-2740-f231-5ef6c547396d-468c82cd"
                                            class="service-div is-number">
                                            <div class="slanted-container is-4">
                                                <div class="body-large">(04)</div><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6817fb1b8e783498a0cbb93b_service-divider-l.svg"
                                                    loading="lazy" alt="" class="slant" />
                                            </div>
                                            <h2 class="heading-3 italic is-service">Post</h2>
                                        </div>
                                        <div id="w-node-b25338df-8c5e-2740-f231-5ef6c5473975-468c82cd"
                                            class="service-div is-text">
                                            <div class="body-large is-service">Editing, motion graphics, animation and
                                                sound design all handled in-house to deliver polished final assets that
                                                impress.</div>
                                        </div>
                                        <div id="w-node-b25338df-8c5e-2740-f231-5ef6c5473977-468c82cd"
                                            class="service-div is-right">
                                            <div class="slanted-container is-right"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6817fb1b8e783498a0cbb93b_service-divider-l.svg"
                                                    loading="lazy" alt="" class="slant" />
                                                <div class="heading-5 italic">+</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <section class="section dark-bg is-sticky">
                            <div class="centre-wrapper is-z">
                                <div class="sub-heading-div">
                                    <h2 data-w-id="a831b389-b2a8-d3a8-8f76-39a3ff15d422" style="opacity:0"
                                        class="subheading centre-align">From punchy social-first promos to cinematic
                                        campaigns</h2>
                                    <div split-text="" class="heading-icon-flex">
                                        <div class="heading-2 centre-align">Powerful</div><img
                                            src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/682e9044e7d6049fa47ad8c8_thinreel-van-white.svg"
                                            loading="lazy"
                                            style="opacity:0;-webkit-transform:translate3d(0, 2rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 2rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 2rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 2rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                            data-w-id="107d75d7-ef3c-c417-c459-00a885718907" alt=""
                                            class="van-illustration is-desktop" />
                                        <div class="heading-2 centre-align">work</div>
                                    </div>
                                    <div split-text="" class="heading-2 centre-align">that performs</div><img
                                        src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/682e9044e7d6049fa47ad8c8_thinreel-van-white.svg"
                                        loading="lazy" alt="" class="van-illustration is-mobile" />
                                    <div class="top-margin-s">
                                        <div class="primary-button"><svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                                viewBox="0 0 20 20" fill="none" class="button-arrow">
                                                <g clip-path="url(#clip0_234_3928)">
                                                    <path
                                                        d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                                        fill="currentColor"></path>
                                                </g>
                                                <defs>
                                                    <clippath id="clip0_234_3928">
                                                        <rect width="19.41" height="20" fill="currentColor"></rect>
                                                    </clippath>
                                                </defs>
                                            </svg><a href="/case-studies" class="underline-link">Our Portfolio</a></div>
                                    </div>
                                </div>
                            </div>
                            <div data-w-id="fdb2bf0e-056a-6f91-ca6c-f5902130ef49" class="featured-wrapper">
                                <div data-w-id="4b64fa8a-4114-b4a7-6054-563296c5e03a" class="sticky-case">
                                    <div data-w-id="857a7f5d-677e-ee05-5e32-d7e63edbb6d5" class="blur-target">
                                        <div class="featured-video">
                                            <div class="desktop-only-video">
                                                <div data-w-id="6b05a69a-d045-1231-60af-2cbf76934840"
                                                    class="video-link no-mobile is-3 w-embed w-iframe">
                                                    <div data-video-id="33416"
                                                        style="width: 100%; height: 100%;overflow: hidden;">

                                                        <iframe width="100%" height="100%"
                                                            src="https://app.vidzflow.com/v/S15UmOhXiM?dq=576&ap=true&muted=true&loop=true&ctp=false&bv=true&piv=true&playsinline=false&bc=%234E5FFD&controls=false"
                                                            title="Featured Case Study 1" style="overflow: hidden;"
                                                            frameborder="0" scrolling="no" allow="fullscreen"></iframe>
                                                    </div>
                                                </div>
                                                <div data-w-id="67e74de2-1a7c-0feb-9b3f-aec015e0993e"
                                                    class="video-link no-mobile is-2 w-embed w-iframe">
                                                    <div data-video-id="33416"
                                                        style="width: 100%; height: 100%;overflow: hidden;">

                                                        <iframe width="100%" height="100%"
                                                            src="https://app.vidzflow.com/v/rGvb5hzmy2?dq=576&ap=true&muted=true&loop=true&ctp=false&bv=true&piv=true&playsinline=false&bc=%234E5FFD&controls=false"
                                                            title="Featured Case Study 1" style="overflow: hidden;"
                                                            frameborder="0" scrolling="no" allow="fullscreen"></iframe>
                                                    </div>
                                                </div>
                                                <div class="video-link no-mobile w-embed w-iframe">
                                                    <div data-video-id="33416"
                                                        style="width: 100%; height: 100%;overflow: hidden;">

                                                        <iframe width="100%" height="100%"
                                                            src="https://app.vidzflow.com/v/NQGsf7XC37?dq=576&ap=true&muted=true&loop=true&ctp=false&bv=true&piv=true&playsinline=false&bc=%234E5FFD&controls=false"
                                                            title="Featured Case Study 1" style="overflow: hidden;"
                                                            frameborder="0" scrolling="no" allow="fullscreen"></iframe>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div><a data-cursor="Herbalife Evolution" href="/case-studies/herbalife"
                                    class="featured-work w-inline-block">
                                    <div class="feature-flex">
                                        <div split-text="" class="w-layout-grid _12-column-grid is-work">
                                            <div id="w-node-_02921fb7-c3c2-6bfc-181e-2813b3ece347-468c82cd">
                                                <h3 class="slanted-sub is-work">Evolution</h3>
                                            </div>
                                            <div id="w-node-_99c59dda-8f54-9422-b57a-b2e53bea5fab-468c82cd">
                                                <div class="slanted-sub">Herbalife</div>
                                            </div>
                                            <div id="w-node-a8239071-8e2a-8b38-5338-f8f773fbae30-468c82cd"
                                                class="tags-flex">
                                                <div class="tag">
                                                    <div>Social Campaign</div>
                                                </div>
                                                <div class="tag">
                                                    <div>Lifestyle</div>
                                                </div>
                                                <div class="tag">
                                                    <div>EMEA</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="overlay"></div><img loading="lazy"
                                        src="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68142564bd4ede3d27e71055_230904_Herbalife_DAY_2_HIIT_Drinking_3697.jpg"
                                        alt="" sizes="100vw"
                                        srcset="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68142564bd4ede3d27e71055_230904_Herbalife_DAY_2_HIIT_Drinking_3697-p-500.jpg 500w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68142564bd4ede3d27e71055_230904_Herbalife_DAY_2_HIIT_Drinking_3697-p-800.jpg 800w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68142564bd4ede3d27e71055_230904_Herbalife_DAY_2_HIIT_Drinking_3697-p-1080.jpg 1080w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68142564bd4ede3d27e71055_230904_Herbalife_DAY_2_HIIT_Drinking_3697-p-1600.jpg 1600w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68142564bd4ede3d27e71055_230904_Herbalife_DAY_2_HIIT_Drinking_3697-p-2000.jpg 2000w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68142564bd4ede3d27e71055_230904_Herbalife_DAY_2_HIIT_Drinking_3697.jpg 2500w"
                                        class="image-parallax" />
                                </a><a data-cursor="Touch Grass" data-w-id="fdb2bf0e-056a-6f91-ca6c-f5902130ef50"
                                    href="/case-studies/ordnance-survey-touch-grass-challenge"
                                    class="featured-work w-inline-block">
                                    <div class="image-parallax is-off w-embed">
                                        <div style="width: 100%; height: 100%;"
                                            class="w-background-video w-background-video-atom">
                                            <video playsinline loop muted autoplay="" loop="" muted="" playsinline=""
                                                data-wf-ignore="true" data-object-fit="cover">
                                                <source
                                                    src="https://player.vimeo.com/progressive_redirect/playback/1065631884/rendition/1080p/file.mp4?loc=external&signature=d7af0d363e0a089194351bb7cec968b3a2996dacaccfb2c00b2102e66a750ac8&user_id=233425881"
                                                    data-wf-ignore="true" />
                                            </video>
                                        </div>
                                    </div><img loading="lazy"
                                        src="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6836db55d5a867c877473873_img0015.jpg"
                                        alt="" sizes="100vw"
                                        srcset="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6836db55d5a867c877473873_img0015-p-500.jpg 500w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6836db55d5a867c877473873_img0015-p-800.jpg 800w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6836db55d5a867c877473873_img0015-p-1080.jpg 1080w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6836db55d5a867c877473873_img0015.jpg 1968w"
                                        class="image-parallax" />
                                    <div class="feature-flex">
                                        <div split-text="" class="w-layout-grid _12-column-grid is-work">
                                            <div id="w-node-_9d92c9b9-c0da-3324-b58e-7012626a2040-468c82cd">
                                                <h3 class="slanted-sub is-work">Touch Grass</h3>
                                            </div>
                                            <div id="w-node-_9d92c9b9-c0da-3324-b58e-7012626a2042-468c82cd">
                                                <div class="slanted-sub">Ordnance Survey</div>
                                            </div>
                                            <div id="w-node-_9d92c9b9-c0da-3324-b58e-7012626a2044-468c82cd"
                                                class="tags-flex">
                                                <div class="tag">
                                                    <div>Social-first</div>
                                                </div>
                                                <div class="tag">
                                                    <div>Outdoor</div>
                                                </div>
                                                <div class="tag">
                                                    <div>Gen-Z</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="overlay"></div>
                                </a><a data-cursor="Ford Transit Custom"
                                    data-w-id="fdb2bf0e-056a-6f91-ca6c-f5902130ef57"
                                    href="/case-studies/ford-transit-custom" class="featured-work w-inline-block"><img
                                        loading="lazy"
                                        src="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681426160347a3ec7b66f958_DSCF4608.jpg"
                                        alt="" sizes="100vw"
                                        srcset="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681426160347a3ec7b66f958_DSCF4608-p-500.jpg 500w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681426160347a3ec7b66f958_DSCF4608-p-800.jpg 800w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681426160347a3ec7b66f958_DSCF4608-p-1080.jpg 1080w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681426160347a3ec7b66f958_DSCF4608-p-1600.jpg 1600w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681426160347a3ec7b66f958_DSCF4608-p-2000.jpg 2000w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/681426160347a3ec7b66f958_DSCF4608.jpg 2705w"
                                        class="image-parallax" />
                                    <div class="feature-flex">
                                        <div split-text="" class="w-layout-grid _12-column-grid is-work">
                                            <div id="w-node-_0ab50b70-3d9d-dc0b-3e33-32a1b7b5aa01-468c82cd">
                                                <h3 class="slanted-sub is-work">Transit Custom Creators</h3>
                                            </div>
                                            <div id="w-node-_0ab50b70-3d9d-dc0b-3e33-32a1b7b5aa03-468c82cd">
                                                <div class="slanted-sub">Ford</div>
                                            </div>
                                            <div id="w-node-_0ab50b70-3d9d-dc0b-3e33-32a1b7b5aa05-468c82cd"
                                                class="tags-flex">
                                                <div class="tag">
                                                    <div>Documentary</div>
                                                </div>
                                                <div class="tag">
                                                    <div>Automotive</div>
                                                </div>
                                                <div class="tag">
                                                    <div>Entrepreneurs</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="overlay"></div>
                                </a>
                                <div class="cursor">
                                    <div class="view-text">View</div>
                                    <p class="cursor-paragraph">Learn more</p>
                                </div>
                            </div>
                        </section>
                        <section class="section small-base dark-bg is-z">
                            <div class="sub-heading-div">
                                <div class="hero-container">
                                    <div data-w-id="37152e67-296a-51f5-08f1-d3f3f5bc9557" style="opacity:0"
                                        class="subheading centre-align">On brief and on budget, from pitch to post.
                                    </div>
                                    <div split-text="" class="subheading-div">
                                        <div class="heading-1 centre-align">We&#x27;re all in as</div>
                                        <div class="hero-title-div">
                                            <h2 class="heading-1 is-allies">agency<span class="span-wrapper is-allies">
                                                </span>allies</h2><img
                                                src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/682294e90ce71738d3633171_partnership-dark.svg"
                                                loading="lazy"
                                                style="opacity:0;-webkit-transform:translate3d(0, 2rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 2rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 2rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 2rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                                data-w-id="93e2952a-bfe4-2f59-8d1a-9f77ac64473c" alt=""
                                                class="partnership-img" />
                                        </div>
                                    </div>
                                    <div data-w-id="a9a8341a-8640-08af-6fca-d0e7be309d54" style="opacity:0"
                                        class="centre-text-content">
                                        <div class="body-large centre-align">We’ve spent years inside and alongside
                                            agencies. So we know how much this matters. We know what clients expect. And
                                            we know the risks of working with the wrong production partners.</div>
                                        <p class="centre-align">That’s why we’re all in as agency allies. We’re there
                                            whether you need to bounce some ideas off us, or bring us in to tell a
                                            brand’s story. Because you can find a million production teams who just
                                            point and shoot from a distance. We’d rather be in the trenches with you.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="w-layout-grid _12-column-grid side-padding is-logo">
                                <div id="w-node-_93faafc5-c1f0-6586-077c-5986bd365369-468c82cd" class="logo-wrapper">
                                    <div class="body-large centre-align">Alongside amazing agencies worldwide</div>
                                    <div data-css-marquee="" class="marquee-css is-logo">
                                        <div data-css-marquee-list="" class="marquee-css__list">
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6813128afaae22afb20cff2a_walker-logo.svg"
                                                    loading="lazy" alt="Walker Logo" class="logo" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6813128929cc0e966ae525de_social-logo.svg"
                                                    loading="lazy" alt="Born Social Logo" class="logo is-2" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/681312891ee3837d6b51ddac_crowd-logo.svg"
                                                    loading="lazy" alt="Crowd Logo" class="logo" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68131289aeea00b106851333_wasserman-logo.svg"
                                                    loading="lazy" alt="Wasserman Logo" class="logo" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6813128a98552b090d26ac48_dentsu-logo.svg"
                                                    loading="lazy" alt="Dentsu Logo" class="logo is-short" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68131289402206ba9d4d12c8_croud-logo.svg"
                                                    loading="lazy" alt="Croud Logo" class="logo" /></div>
                                        </div>
                                        <div class="logo-gradient"></div>
                                        <div class="logo-gradient is-right"></div>
                                    </div>
                                </div>
                                <div id="w-node-_1bb4aae0-35ee-7527-e9d1-87cafc2dd679-468c82cd" class="logo-wrapper">
                                    <div class="body-large centre-align">Helping brands perform brilliantly</div>
                                    <div data-css-marquee="" class="marquee-css is-logo">
                                        <div data-css-marquee-list="" class="marquee-css__list">
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68130f750d98ca2ca66ad18c_ford-logo.svg"
                                                    loading="lazy" alt="Ford Logo" class="logo" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68130fd947d617ddb7d060ca_ironman-logo.svg"
                                                    loading="lazy" alt="Ironman Logo" class="logo" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68130fda2196c491043aa9b3_bt-logo.svg"
                                                    loading="lazy" alt="BT Logo" class="logo is-short" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68130fd929cc0e966ae36e47_herbalife-logo.svg"
                                                    loading="lazy" alt="Herbalife Logo" class="logo" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68130fd9402206ba9d4b48bc_att-logo.svg"
                                                    loading="lazy" alt="AT&amp;T Logo" class="logo is-2" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68130fdabd59c3c7ef5fef97_lush-logo.svg"
                                                    loading="lazy" alt="Lush Logo" class="logo" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68130fd9aabf1907aeb241d1_survey-logo.svg"
                                                    loading="lazy" alt="Ordnance Survey Logo" class="logo is-3" /></div>
                                            <div class="marquee-css__item is-logo"><img
                                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68130fd9be8cb4ac995407eb_logitech-logo.svg"
                                                    loading="lazy" alt="Logitech Logo" class="logo" /></div>
                                        </div>
                                        <div class="logo-gradient"></div>
                                        <div class="logo-gradient is-right"></div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <section class="cta-section">
                            <div class="roll-text">
                                <h3 class="roll-title">Ready to roll?</h3>
                            </div>
                            <div class="cta-flex"><a data-modal-target="modal-b" href="#"
                                    class="mid-cta w-inline-block"><img
                                        src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6818532ba836308fc8c83ebb_cta-divider.svg"
                                        loading="lazy" alt="" class="cta-divider" />
                                    <div class="primary-button"><svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                            viewBox="0 0 20 20" fill="none" class="button-arrow is-white">
                                            <g clip-path="url(#clip0_234_3928)">
                                                <path
                                                    d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                                    fill="currentColor"></path>
                                            </g>
                                            <defs>
                                                <clippath id="clip0_234_3928">
                                                    <rect width="19.41" height="20" fill="currentColor"></rect>
                                                </clippath>
                                            </defs>
                                        </svg>
                                        <div class="footer-btn-div">
                                            <div class="text-flex">
                                                <div class="footer-btn">I need help on</div>
                                                <div class="footer-btn is-bold">a pitch</div>
                                            </div>
                                        </div>
                                    </div>
                                </a><a data-modal-target="modal-a" href="#" class="mid-cta w-inline-block"><img
                                        src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6818532ba836308fc8c83ebb_cta-divider.svg"
                                        loading="lazy" alt="" class="cta-divider" />
                                    <div class="primary-button"><svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                            viewBox="0 0 20 20" fill="none" class="button-arrow is-white">
                                            <g clip-path="url(#clip0_234_3928)">
                                                <path
                                                    d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                                    fill="currentColor"></path>
                                            </g>
                                            <defs>
                                                <clippath id="clip0_234_3928">
                                                    <rect width="19.41" height="20" fill="currentColor"></rect>
                                                </clippath>
                                            </defs>
                                        </svg>
                                        <div class="footer-btn-div">
                                            <div class="text-flex">
                                                <div class="footer-btn">I have a</div>
                                                <div class="footer-btn is-bold">Possible project</div>
                                            </div>
                                        </div>
                                    </div>
                                </a></div>
                        </section>
                        <section data-w-id="33e2abee-4ac0-e3ff-2287-a3a790b9ee10" class="section is-min-vh">
                            <div class="image-container"><img
                                    src="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/6815587a392b43db29fc3bdc_DSCF6541-usp.jpg"
                                    loading="lazy" alt="Thinreel team in action" class="global-parallax" />
                                <div class="base-gradient"></div>
                            </div>
                            <div class="w-layout-grid _12-column-grid is-usp is-1">
                                <h2 split-text="" id="w-node-_7719e0fc-01ca-3c60-b228-042a3cadbacc-468c82cd"
                                    class="centre-mob">Never just <span class="text-span">a job, always </span><span
                                        class="text-span">a passion project</span></h2>
                            </div>
                            <div class="w-layout-grid _12-column-grid is-usp">
                                <div id="w-node-ba468a6e-39c1-002e-0dec-07b6d6cf27d1-468c82cd" class="usp-div is-1">
                                    <div class="w-layout-grid _4-column-grid"><img
                                            src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/681822b6e8a9844055e702db_big-brief-1.svg"
                                            loading="lazy" alt="An illustration of a giant brief document"
                                            class="usp-icon" />
                                        <div id="w-node-_22908c93-24a4-4261-a6eb-8b231c39595a-468c82cd"
                                            class="usp-text">
                                            <h3 class="heading-5 is-cm">Big brief or tight turnaround</h3>
                                            <p class="is-white-text is-cm">Production doesn’t need to be complicated.
                                                We’re built for agencies that need quality content without the delays,
                                                dramas or budget creep.</p>
                                        </div>
                                    </div>
                                </div>
                                <div id="w-node-_2a35c762-41da-fd28-c63b-00a4dbb8584a-468c82cd" class="usp-div">
                                    <div class="w-layout-grid _4-column-grid"><img
                                            src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6818239fea635a7312c97763_on-location-1.svg"
                                            loading="lazy" id="w-node-_60756314-757b-ff8c-3cbf-a35d576d0bc8-468c82cd"
                                            alt="An illustration of a van in a city" class="usp-icon" />
                                        <div id="w-node-_2a35c762-41da-fd28-c63b-00a4dbb858a2-468c82cd"
                                            class="usp-text">
                                            <h3 class="heading-5 is-cm">A love for on location</h3>
                                            <p class="is-white-text is-cm">From city streets to remote landscapes, we
                                                thrive on location. Our in-house crew is built for dynamic shoots
                                                wherever the creative takes us.</p>
                                        </div>
                                    </div>
                                </div>
                                <div id="w-node-_1e5d310e-7053-7817-a943-96557b45e64e-468c82cd" class="usp-div">
                                    <div class="w-layout-grid _4-column-grid"><img
                                            src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68182424e86f67f9a4af6106_in-house-1.svg"
                                            loading="lazy" id="w-node-_036a8699-2eaf-1ad1-6f04-2616dc685417-468c82cd"
                                            alt="An illustration of some film kit" class="usp-icon" />
                                        <div id="w-node-_1e5d310e-7053-7817-a943-96557b45e6a6-468c82cd"
                                            class="usp-text">
                                            <h3 class="heading-5 is-cm">All handled in-house</h3>
                                            <p class="is-white-text is-cm">You bring the big ideas, we make them happen.
                                                With a full in-house crew and kit, we turn strategies and concepts into
                                                standout content clients love.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <section class="minifesto-section">
                            <div data-w-id="9b757455-466a-0e13-10ea-660883a779d1" class="minifesto-image-container"><img
                                    src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6815609e94994ee2aa2aaff1_DSCF5249minifesto.jpg"
                                    loading="lazy" sizes="(max-width: 2486px) 100vw, 2486px"
                                    srcset="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6815609e94994ee2aa2aaff1_DSCF5249minifesto-p-500.jpg 500w, https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6815609e94994ee2aa2aaff1_DSCF5249minifesto-p-800.jpg 800w, https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6815609e94994ee2aa2aaff1_DSCF5249minifesto-p-1080.jpg 1080w, https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6815609e94994ee2aa2aaff1_DSCF5249minifesto-p-1600.jpg 1600w, https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6815609e94994ee2aa2aaff1_DSCF5249minifesto-p-2000.jpg 2000w, https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6815609e94994ee2aa2aaff1_DSCF5249minifesto.jpg 2486w"
                                    alt="Thinreel team walking at sunset" class="global-parallax" />
                                <div class="minifesto-left">
                                    <div data-w-id="0c1dcec3-5269-dac3-4c95-2948c8f8e6c2" style="opacity:0"
                                        class="subheading is-dark-text">How we like to do things</div>
                                    <div class="minifesto-title">
                                        <h2 split-text="" class="centre-align is-dark-text">Think of this as our
                                            minifesto</h2>
                                        <div class="top-margin-s">
                                            <div class="primary-button"><svg xmlns="http://www.w3.org/2000/svg"
                                                    width="100%" viewBox="0 0 20 20" fill="none" class="button-arrow">
                                                    <g clip-path="url(#clip0_234_3928)">
                                                        <path
                                                            d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                                            fill="currentColor"></path>
                                                    </g>
                                                    <defs>
                                                        <clippath id="clip0_234_3928">
                                                            <rect width="19.41" height="20" fill="currentColor"></rect>
                                                        </clippath>
                                                    </defs>
                                                </svg><a href="/about" class="underline-link is-dark">ABout us</a></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mobile-gradient"></div>
                            </div>
                            <div class="minifesto-container">
                                <div class="div-block-6">
                                    <div data-w-id="d67656ec-500c-4ab1-5371-8ce29ce0a35c" class="minifesto-right">
                                        <div class="minifesto-card">
                                            <div class="minifesto is-number">01</div>
                                            <div id="w-node-_375d4e62-7cb8-014a-50bb-b313dba08b6b-468c82cd"
                                                class="card-content">
                                                <h3 class="minifesto">Being as green as possible</h3>
                                                <p class="minifesto-body">We follow AdGreen standards, keep travel lean
                                                    and power post with renewable energy. From using local crew to
                                                    offsetting our shoots, we make sustainable production choices that
                                                    don’t cost the earth.</p>
                                            </div>
                                        </div>
                                        <div class="minifesto-card is-2">
                                            <div class="minifesto is-number">02</div>
                                            <div id="w-node-_77caef5f-99b1-1a54-e1c4-4aa4c829d25f-468c82cd"
                                                class="card-content">
                                                <h3 class="minifesto">Making fees less of a fuss</h3>
                                                <p class="minifesto-body">All-in pricing. No curveballs. We scope smart
                                                    and quote transparently so you’re never stuck justifying surprise
                                                    add-ons. From pre to post, you’ll know exactly what’s included and
                                                    the costs involved.</p>
                                            </div>
                                        </div>
                                        <div class="minifesto-card is-3">
                                            <div class="minifesto is-number">03</div>
                                            <div id="w-node-_81f56180-48c9-904f-2bd7-855910623ea9-468c82cd"
                                                class="card-content">
                                                <h3 class="minifesto">Respecting your relationships</h3>
                                                <p class="minifesto-body">We work alongside you as production partners,
                                                    not competition or client poachers. Agencies trust us because we act
                                                    like part of the team, backing you up without ever taking the
                                                    spotlight.</p>
                                            </div>
                                        </div>
                                        <div class="minifesto-card is-4">
                                            <div class="minifesto is-number">04</div>
                                            <div id="w-node-_5bc7f40f-aab2-8771-f45d-aa892d5fe043-468c82cd"
                                                class="card-content">
                                                <h3 class="minifesto">Keeping all comms clear</h3>
                                                <p class="minifesto-body">We stay in sync from day one. You’ll always
                                                    know who to speak to and where things stand. No radio silence. No
                                                    crossed wires. Just clear, proactive updates throughout from
                                                    planning to post, feedback to final delivery</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="div-block-7"></div>
                                </div>
                            </div>
                            <div class="minifesto-mobile">
                                <div data-accordion-close-siblings="true" data-accordion-css-init=""
                                    class="accordion-css">
                                    <ul class="accordion-css__list">
                                        <li data-accordion-status="not-active" class="accordion-css__item">
                                            <div data-hover="" data-accordion-toggle="" class="accordion-css__item-top">
                                                <div class="div-block-14">
                                                    <div class="accordion-css__item-h3 is-number">01</div>
                                                    <div class="accordion-css__item-h3">Being as green as possible</div>
                                                </div>
                                                <div class="accordion-css__item-icon"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="100%"
                                                        viewBox="0 0 9 11" fill="none"
                                                        class="accordion-css__item-icon-svg">
                                                        <path
                                                            d="M3.57806 10.2759V6.32794H0.414062V4.31194H3.57806V0.335938H5.67806V4.31194H8.87006V6.32794H5.67806V10.2759H3.57806Z"
                                                            fill="currentColor"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="accordion-css__item-bottom">
                                                <div class="accordion-css__item-bottom-wrap">
                                                    <div class="accordion-css__item-bottom-content">
                                                        <p class="accordion-css__item-p">We follow AdGreen standards,
                                                            keep travel lean and power post with renewable energy. From
                                                            using local crew to offsetting our shoots, we make
                                                            sustainable production choices that don’t cost the earth.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li data-accordion-status="not-active" class="accordion-css__item">
                                            <div data-hover="" data-accordion-toggle="" class="accordion-css__item-top">
                                                <div class="div-block-14">
                                                    <div class="accordion-css__item-h3 is-number">02</div>
                                                    <div class="accordion-css__item-h3">Making fees less of a fuss</div>
                                                </div>
                                                <div class="accordion-css__item-icon"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="100%"
                                                        viewBox="0 0 9 11" fill="none"
                                                        class="accordion-css__item-icon-svg">
                                                        <path
                                                            d="M3.57806 10.2759V6.32794H0.414062V4.31194H3.57806V0.335938H5.67806V4.31194H8.87006V6.32794H5.67806V10.2759H3.57806Z"
                                                            fill="currentColor"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="accordion-css__item-bottom">
                                                <div class="accordion-css__item-bottom-wrap">
                                                    <div class="accordion-css__item-bottom-content">
                                                        <p class="accordion-css__item-p">All-in pricing. No curveballs.
                                                            We scope smart and quote transparently so you’re never stuck
                                                            justifying surprise add-ons. From pre to post, you’ll know
                                                            exactly what’s included and the costs involved.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li data-accordion-status="not-active" class="accordion-css__item">
                                            <div data-hover="" data-accordion-toggle="" class="accordion-css__item-top">
                                                <div class="div-block-14">
                                                    <div class="accordion-css__item-h3 is-number">03</div>
                                                    <div class="accordion-css__item-h3">Respecting your relationships
                                                    </div>
                                                </div>
                                                <div class="accordion-css__item-icon"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="100%"
                                                        viewBox="0 0 9 11" fill="none"
                                                        class="accordion-css__item-icon-svg">
                                                        <path
                                                            d="M3.57806 10.2759V6.32794H0.414062V4.31194H3.57806V0.335938H5.67806V4.31194H8.87006V6.32794H5.67806V10.2759H3.57806Z"
                                                            fill="currentColor"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="accordion-css__item-bottom">
                                                <div class="accordion-css__item-bottom-wrap">
                                                    <div class="accordion-css__item-bottom-content">
                                                        <p class="accordion-css__item-p">We work alongside you as
                                                            production partners, not competition or client poachers.
                                                            Agencies trust us because we act like part of the team,
                                                            backing you up without ever taking the spotlight.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li data-accordion-status="not-active" class="accordion-css__item">
                                            <div data-hover="" data-accordion-toggle="" class="accordion-css__item-top">
                                                <div class="div-block-14">
                                                    <div class="accordion-css__item-h3 is-number">04</div>
                                                    <div class="accordion-css__item-h3">Keeping all comms clear</div>
                                                </div>
                                                <div class="accordion-css__item-icon"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="100%"
                                                        viewBox="0 0 9 11" fill="none"
                                                        class="accordion-css__item-icon-svg">
                                                        <path
                                                            d="M3.57806 10.2759V6.32794H0.414062V4.31194H3.57806V0.335938H5.67806V4.31194H8.87006V6.32794H5.67806V10.2759H3.57806Z"
                                                            fill="currentColor"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="accordion-css__item-bottom">
                                                <div class="accordion-css__item-bottom-wrap">
                                                    <div class="accordion-css__item-bottom-content">
                                                        <p class="accordion-css__item-p">We stay in sync from day one.
                                                            You’ll always know who to speak to and where things stand.
                                                            No radio silence. No crossed wires. Just clear, proactive
                                                            updates throughout from planning to post, feedback to final
                                                            delivery</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </section>
                        <section data-w-id="7b4f3ee6-58c7-fe6c-17a1-bd32004714e3" class="drone-section dark-bg">
                            <div data-w-id="a90b46d8-e6ca-55f2-701c-bc7055e6c315" class="quote-video-flex">
                                <div data-w-id="63420a54-b39e-15a3-27b0-5908f890f72c"
                                    class="img-div is-portrait is-drone-1">
                                    <div class="desktop-only-video">
                                        <div class="video-link w-embed w-iframe">
                                            <div data-video-id="33416"
                                                style="width: 100%; height: 100%;overflow: hidden;">

                                                <iframe width="100%" height="100%"
                                                    src="https://app.vidzflow.com/v/am2uis0ObU?dq=576&ap=true&muted=true&loop=true&ctp=false&bv=true&piv=true&playsinline=false&bc=%234E5FFD&controls=false"
                                                    title="ThinReel Social Content" style="overflow: hidden;"
                                                    frameborder="0" scrolling="no" allow="fullscreen"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div data-w-id="e571aeff-5f21-57e8-6a74-d0758dfed17d"
                                    class="img-div is-portrait is-drone-1">
                                    <div class="desktop-only-video">
                                        <div class="video-link w-embed w-iframe">
                                            <div data-video-id="33416"
                                                style="width: 100%; height: 100%;overflow: hidden;">

                                                <iframe width="100%" height="100%"
                                                    src="https://app.vidzflow.com/v/FSMTvL4mlT?dq=576&ap=true&muted=true&loop=true&ctp=false&bv=true&piv=true&playsinline=false&bc=%234E5FFD&controls=false"
                                                    title="Thinreel Case Study Snapshot" style="overflow: hidden;"
                                                    frameborder="0" scrolling="no" allow="fullscreen"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div data-is-ix2-target="1" class="drone-lottie"
                                data-w-id="0f38d343-67b8-f278-9508-4b205c60337c" data-animation-type="lottie"
                                data-src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6827e73087bdeca18c626497_Drone_Text_01.json"
                                data-loop="0" data-direction="1" data-autoplay="0" data-renderer="svg"
                                data-default-duration="0" data-duration="6"></div>
                            <div data-w-id="325f8bf4-ce2f-0b3b-df77-545fe26513a4" data-is-ix2-target="1"
                                class="drone-operator" data-animation-type="lottie"
                                data-src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/68325f87f0ec97bef145a558_Drone_Pilot_Lottie_01.json"
                                data-loop="1" data-direction="1" data-autoplay="0" data-renderer="svg"
                                data-default-duration="0" data-duration="2"></div>
                            <div class="quote-flex">
                                <div class="heading-2 centre-align">“we’ve amassed some incredible pieces of creative
                                    that everyone’s proud of”</div>
                                <div class="quote-credit">
                                    <div class="body-large centre-align">Zennen Thomas</div>
                                    <p class="centre-align">Walker Agency</p>
                                </div>
                            </div>
                        </section>
                        <section class="section is-people dark-bg">
                            <div class="w-layout-grid _12-column-grid is-base">
                                <div id="w-node-_30ac74a5-6a1a-15af-ef6a-1a3144a954f1-468c82cd"
                                    data-w-id="30ac74a5-6a1a-15af-ef6a-1a3144a954f1" class="image-wrap"><img
                                        src="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68158bc15fcc8db5a1eaf381_DSCF1025.jpg"
                                        loading="lazy" alt="" sizes="100vw"
                                        srcset="https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68158bc15fcc8db5a1eaf381_DSCF1025-p-500.jpg 500w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68158bc15fcc8db5a1eaf381_DSCF1025-p-800.jpg 800w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68158bc15fcc8db5a1eaf381_DSCF1025-p-1080.jpg 1080w, https://cdn.prod.website-files.com/68108200d9cc816e387d7156/68158bc15fcc8db5a1eaf381_DSCF1025.jpg 1777w"
                                        class="global-parallax" /></div>
                                <div id="w-node-_16359b79-f7f3-c98c-643b-ed5fa264649e-468c82cd" class="div-block-12">
                                    <h2 split-text="" class="heading-3 is-inline is-larger">The right people, <span
                                            class="text-span">right there</span> <span class="text-span">with
                                            you.</span></h2>
                                    <div data-w-id="00821cf3-a2e7-dc1f-3651-7497cb70b63d" style="opacity:0"
                                        class="left-block">
                                        <div data-w-id="00821cf3-a2e7-dc1f-3651-7497cb70b63e" class="body-large">Of
                                            course we’re obsessed with brand storytelling and visual narratives. These
                                            are table stakes for any creative production company.</div>
                                        <p>But in the chaos of a client project, you need friendly people you can bounce
                                            ideas off. Problem solvers who go further to find a great angle on your
                                            ideas. And a team that takes the weight off because you trust they’ll get it
                                            done, and done with style.</p>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div data-w-id="42ebee75-3fcc-d6a4-4219-337660f1bedf" class="footer-reveal-wrap">
        <div class="footer-reveal-mask">
            <section class="footer-target">
                <div class="footer-container">
                    <div class="navigation-wrap is-footer">
                        <div class="w-layout-grid _12-column-grid is-nav">
                            <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1bee5-60f1bedf" class="nav-flex no-mobile">
                                <a href="/about" class="underline-link is-nav">About Us</a><a href="/case-studies"
                                    class="underline-link is-nav">Our Work</a><a href="/our-world"
                                    class="underline-link is-nav">Our World</a></div>
                            <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1beec-60f1bedf"
                                class="logo-container is-footer"><a href="/" aria-current="page"
                                    class="logo-link w-inline-block w--current"><svg xmlns="http://www.w3.org/2000/svg"
                                        width="100%" viewBox="0 0 131 27" fill="none" class="logo-svg is-footer">
                                        <g clip-path="url(#clip0_202_7126)">
                                            <path
                                                d="M11.9946 3.65328C9.10209 3.65328 4.90223 3.64597 4.43135 3.65328C5.12597 2.34302 5.64218 1.37495 6.37043 0.00326253C6.81352 0.00326253 25.4599 0.000337831 25.7714 0.00326253C25.087 1.29159 24.3778 2.62525 23.8309 3.65328C22.2252 3.65328 16.6288 3.65036 16.2661 3.65328C14.9003 6.22117 6.17593 22.6302 4.81449 25.1893H0.542969C2.34604 21.7996 10.8759 5.75614 11.9946 3.65328Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M53.5913 6.07459H57.3729C56.9327 6.91691 50.4194 19.167 50.4194 19.167H47.3968C47.3968 19.167 45.4124 12.8189 44.9342 11.3317C44.3609 12.4095 41.5152 17.7631 40.7679 19.167H36.9717C38.5408 16.216 41.7667 10.1487 43.9339 6.07459H46.5998C47.3704 8.81211 48.2888 11.8333 49.0828 14.565C49.6342 13.5165 52.9873 7.20937 53.5913 6.07459Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M21.8481 19.167C22.8249 17.3303 23.54 15.9849 24.4394 14.293H18.1717C17.2198 16.0844 16.3731 17.6754 15.5805 19.167H11.7988C12.6075 17.6476 18.0328 7.45359 18.7611 6.07459H22.5427C21.8861 7.30881 20.434 10.0419 19.95 10.9515C20.959 10.9515 24.5432 10.9369 26.2176 10.9515C26.747 9.95566 28.4871 6.64637 28.8103 6.07459H32.592C32.1299 6.94323 26.3653 17.7865 25.6297 19.167H21.8481Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M85.3851 16.4514H93.1385C92.4 17.8406 92.071 18.4578 91.6937 19.167C88.6052 19.1597 83.2574 19.1714 80.1689 19.167C80.8519 17.8831 85.8034 8.56936 87.1312 6.07459C88.304 6.06289 97.3574 6.07459 98.6559 6.07459C97.9979 7.31174 97.8166 7.65247 97.2185 8.77847C95.2984 8.76824 89.4651 8.77847 89.4651 8.77847L88.1402 11.2703H92.1105L90.673 13.9742H86.7027L85.3866 16.45L85.3851 16.4514Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M100.689 16.4514H108.442C107.704 17.8406 107.375 18.4578 106.997 19.167C103.909 19.1597 98.5611 19.1714 95.4727 19.167C96.1556 17.8831 101.107 8.56936 102.435 6.07459C103.608 6.06289 112.661 6.07459 113.96 6.07459C113.302 7.31174 113.12 7.65247 112.522 8.77847C110.602 8.76824 106.718 8.77847 104.769 8.77847C104.353 9.56083 103.671 10.8448 103.444 11.2703H107.413L105.975 13.9742H102.006C101.736 14.4846 100.876 16.1004 100.69 16.45L100.689 16.4514Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M115.988 16.4514H122.254L120.811 19.167H110.771L117.734 6.07459H121.514L115.988 16.4514Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M36.3753 6.07459C37.3332 6.08044 39.1567 6.07459 40.1526 6.07459C39.629 7.06022 33.6978 18.2135 33.1903 19.167H29.4131C29.6368 18.7093 35.9191 6.93007 36.3753 6.07459Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M80.8803 2.13795C79.7118 0.745797 78.0053 0.00292397 76.0706 0.00292397C75.7606 0.00292397 64.3923 0.00292397 64.3923 0.00292397L62.4518 3.65294H66.2349L54.7832 25.1889H59.0547L64.8471 14.293H69.1054C69.1054 14.293 75.4286 26.2067 75.6421 26.5869C76.4245 26.2667 78.5493 25.3454 78.9412 25.2006C77.201 21.9264 73.1415 14.293 73.1415 14.293H74.2675C74.6741 14.293 75.0806 14.2696 75.4798 14.1994C78.7789 13.6262 81.5866 10.7409 82.1832 7.36292C82.524 5.43408 82.0472 3.53011 80.8788 2.13649L80.8803 2.13795ZM78.5917 6.73119C78.2451 8.69658 76.4801 10.9515 73.9721 10.9515C72.3738 10.9559 71.2273 10.9515 69.6304 10.9515H66.6268L70.5078 3.65148H76.2198C76.9919 3.68658 77.6514 3.96442 78.0857 4.4821C78.5668 5.05534 78.7467 5.85378 78.5932 6.72973L78.5917 6.73119Z"
                                                fill="currentColor"></path>
                                            <path d="M51.0009 25.1889H8.5957L10.5362 21.5404H52.9414L51.0009 25.1889Z"
                                                fill="currentColor"></path>
                                            <path d="M71.1192 25.1889H62.8379L64.7784 21.5404H69.1786L71.1192 25.1889Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M58.6697 3.65295H27.6123L29.5558 0L60.6102 0.0029247L58.6697 3.65295Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M80.7773 21.5404L82.7252 25.2006L117.608 25.1889L119.549 21.5404H80.7773Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M83.4785 0.00731101C84.3574 1.06313 84.9877 2.31197 85.3372 3.65733L129.06 3.65441L131 0.00292397L83.4785 0.00584866V0.00731101Z"
                                                fill="currentColor"></path>
                                        </g>
                                        <defs>
                                            <clippath id="clip0_202_7126">
                                                <rect width="130.458" height="26.5884" fill="currentColor"
                                                    transform="translate(0.542969)"></rect>
                                            </clippath>
                                        </defs>
                                    </svg></a></div>
                            <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1bf00-60f1bedf" class="div-block-13">
                                <div class="nav-hide-mob">
                                    <div class="primary-button"><svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                            viewBox="0 0 20 20" fill="none" class="button-arrow">
                                            <g clip-path="url(#clip0_234_3928)">
                                                <path
                                                    d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                                    fill="currentColor"></path>
                                            </g>
                                            <defs>
                                                <clippath id="clip0_234_3928">
                                                    <rect width="19.41" height="20" fill="currentColor"></rect>
                                                </clippath>
                                            </defs>
                                        </svg><a href="/contact" class="underline-link">Contact Us</a></div>
                                </div>
                                <div class="social-flex is-footer"><a href="#" class="social-div w-inline-block"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 24 24"
                                            fill="none" class="social-icon">
                                            <path
                                                d="M21.94 7.88C21.9206 7.0503 21.7652 6.2294 21.48 5.45C21.2283 4.78181 20.8322 4.17742 20.32 3.68C19.8226 3.16776 19.2182 2.77166 18.55 2.52C17.7706 2.23484 16.9497 2.07945 16.12 2.06C15.06 2 14.72 2 12 2C9.28 2 8.94 2 7.88 2.06C7.0503 2.07945 6.2294 2.23484 5.45 2.52C4.78181 2.77166 4.17742 3.16776 3.68 3.68C3.16743 4.17518 2.77418 4.78044 2.53 5.45C2.23616 6.22734 2.07721 7.04915 2.06 7.88C2 8.94 2 9.28 2 12C2 14.72 2 15.06 2.06 16.12C2.07721 16.9508 2.23616 17.7727 2.53 18.55C2.77418 19.2196 3.16743 19.8248 3.68 20.32C4.17742 20.8322 4.78181 21.2283 5.45 21.48C6.2294 21.7652 7.0503 21.9206 7.88 21.94C8.94 22 9.28 22 12 22C14.72 22 15.06 22 16.12 21.94C16.9497 21.9206 17.7706 21.7652 18.55 21.48C19.2134 21.219 19.816 20.8242 20.3201 20.3201C20.8242 19.816 21.219 19.2134 21.48 18.55C21.7652 17.7706 21.9206 16.9497 21.94 16.12C21.94 15.06 22 14.72 22 12C22 9.28 22 8.94 21.94 7.88ZM20.14 16C20.1327 16.6348 20.0178 17.2637 19.8 17.86C19.6327 18.2913 19.3773 18.683 19.0501 19.0101C18.723 19.3373 18.3313 19.5927 17.9 19.76C17.3037 19.9778 16.6748 20.0927 16.04 20.1C15.04 20.15 14.67 20.16 12.04 20.16C9.41 20.16 9.04 20.16 8.04 20.1C7.38073 20.1148 6.72401 20.0132 6.1 19.8C5.66869 19.6327 5.27698 19.3773 4.94985 19.0501C4.62272 18.723 4.36734 18.3313 4.2 17.9C3.97775 17.2911 3.86271 16.6482 3.86 16C3.86 15 3.8 14.63 3.8 12C3.8 9.37 3.8 9 3.86 8C3.86271 7.35178 3.97775 6.70893 4.2 6.1C4.36734 5.66869 4.62272 5.27698 4.94985 4.94985C5.27698 4.62272 5.66869 4.36734 6.1 4.2C6.70893 3.97775 7.35178 3.86271 8 3.86C9 3.86 9.37 3.8 12 3.8C14.63 3.8 15 3.8 16 3.86C16.6348 3.86728 17.2637 3.98225 17.86 4.2C18.2913 4.36734 18.683 4.62272 19.0101 4.94985C19.3373 5.27698 19.5927 5.66869 19.76 6.1C19.9959 6.7065 20.1245 7.34942 20.14 8C20.19 9 20.2 9.37 20.2 12C20.2 14.63 20.19 15 20.14 16Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M12 6.86C10.9834 6.86 9.98964 7.16146 9.14437 7.72625C8.2991 8.29104 7.64029 9.0938 7.25126 10.033C6.86222 10.9722 6.76044 12.0057 6.95876 13.0028C7.15709 13.9998 7.64663 14.9157 8.36547 15.6345C9.08431 16.3534 10.0002 16.8429 10.9972 17.0412C11.9943 17.2396 13.0278 17.1378 13.967 16.7487C14.9062 16.3597 15.709 15.7009 16.2738 14.8556C16.8385 14.0104 17.14 13.0166 17.14 12C17.14 10.6368 16.5985 9.32941 15.6345 8.36547C14.6706 7.40153 13.3632 6.86 12 6.86ZM12 15.33C11.3414 15.33 10.6976 15.1347 10.15 14.7688C9.60234 14.4029 9.17552 13.8828 8.92348 13.2743C8.67144 12.6659 8.6055 11.9963 8.73399 11.3503C8.86247 10.7044 9.17963 10.111 9.64533 9.64533C10.111 9.17963 10.7044 8.86247 11.3503 8.73399C11.9963 8.6055 12.6659 8.67144 13.2743 8.92348C13.8828 9.17552 14.4029 9.60234 14.7688 10.15C15.1347 10.6976 15.33 11.3414 15.33 12C15.33 12.4373 15.2439 12.8703 15.0765 13.2743C14.9092 13.6784 14.6639 14.0454 14.3547 14.3547C14.0454 14.6639 13.6784 14.9092 13.2743 15.0765C12.8703 15.2439 12.4373 15.33 12 15.33Z"
                                                fill="currentColor"></path>
                                            <path
                                                d="M17.34 5.46001C17.1027 5.46001 16.8707 5.53039 16.6733 5.66224C16.476 5.7941 16.3222 5.98152 16.2313 6.20079C16.1405 6.42006 16.1168 6.66134 16.1631 6.89411C16.2094 7.12689 16.3236 7.34071 16.4915 7.50853C16.6593 7.67636 16.8731 7.79065 17.1059 7.83695C17.3387 7.88325 17.5799 7.85949 17.7992 7.76866C18.0185 7.67784 18.2059 7.52403 18.3378 7.32669C18.4696 7.12935 18.54 6.89734 18.54 6.66001C18.54 6.34175 18.4136 6.03652 18.1885 5.81148C17.9635 5.58643 17.6583 5.46001 17.34 5.46001Z"
                                                fill="currentColor"></path>
                                        </svg></a><a href="#" class="social-div w-inline-block"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 24 24"
                                            fill="none" class="social-icon">
                                            <path
                                                d="M20.9 20.9H17.166V15.053C17.166 13.659 17.138 11.865 15.222 11.865C13.277 11.865 12.98 13.382 12.98 14.95V20.9H9.249V8.87699H12.833V10.516H12.881C13.2402 9.90278 13.7588 9.39838 14.3818 9.05643C15.0048 8.71447 15.7088 8.54775 16.419 8.57399C20.199 8.57399 20.898 11.062 20.898 14.3V20.9H20.9ZM5.036 7.23199C4.60732 7.23259 4.1881 7.10603 3.83137 6.86832C3.47463 6.63061 3.19641 6.29244 3.03191 5.89658C2.8674 5.50072 2.824 5.06497 2.9072 4.64444C2.99039 4.22392 3.19644 3.83751 3.49928 3.53411C3.80212 3.23071 4.18815 3.02395 4.60852 2.93998C5.02889 2.85601 5.46473 2.8986 5.86089 3.06237C6.25705 3.22615 6.59573 3.50374 6.8341 3.86003C7.07246 4.21633 7.1998 4.63532 7.2 5.06399C7.20039 5.34847 7.14472 5.63024 7.03615 5.89319C6.92759 6.15615 6.76827 6.39512 6.5673 6.59647C6.36633 6.79781 6.12764 6.95757 5.86489 7.06662C5.60214 7.17567 5.32048 7.23186 5.036 7.23199ZM6.906 20.9H3.165V8.87699H6.906V20.9Z"
                                                fill="currentColor"></path>
                                        </svg></a></div>
                            </div>
                        </div>
                    </div>
                    <div class="footer-main">
                        <h2 class="heading-1 is-footer">So, where are you at?</h2>
                        <div class="footer-buttons">
                            <div data-w-id="42ebee75-3fcc-d6a4-4219-337660f1bf11" class="primary-button"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 20 20" fill="none"
                                    class="button-arrow is-white">
                                    <g clip-path="url(#clip0_234_3928)">
                                        <path
                                            d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                            fill="currentColor"></path>
                                    </g>
                                    <defs>
                                        <clippath id="clip0_234_3928">
                                            <rect width="19.41" height="20" fill="currentColor"></rect>
                                        </clippath>
                                    </defs>
                                </svg><a data-modal-target="modal-b" href="#" class="footer-btn-div w-inline-block">
                                    <div class="text-flex">
                                        <div class="footer-btn">I need help on</div>
                                        <div class="footer-btn is-bold">a pitch</div>
                                    </div>
                                    <div class="footer-underline">
                                        <div class="underline-active"></div>
                                    </div>
                                </a></div>
                            <div class="mobile-divider"></div>
                            <div data-w-id="42ebee75-3fcc-d6a4-4219-337660f1bf21" class="primary-button"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 20 20" fill="none"
                                    class="button-arrow is-white">
                                    <g clip-path="url(#clip0_234_3928)">
                                        <path
                                            d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                            fill="currentColor"></path>
                                    </g>
                                    <defs>
                                        <clippath id="clip0_234_3928">
                                            <rect width="19.41" height="20" fill="currentColor"></rect>
                                        </clippath>
                                    </defs>
                                </svg><a data-modal-target="modal-a" href="#" class="footer-btn-div w-inline-block">
                                    <div class="text-flex">
                                        <div class="footer-btn">I have a</div>
                                        <div class="footer-btn is-bold">Possible Project</div>
                                    </div>
                                    <div class="footer-underline">
                                        <div class="underline-active"></div>
                                    </div>
                                </a></div>
                        </div>
                    </div>
                    <div class="w-layout-grid _12-column-grid is-footer">
                        <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1bf31-60f1bedf"
                            class="footer-div is-base and-top">
                            <div class="body-large">London</div>
                            <p>167-169 Great Portland Street,<br />5th Floor, London,<br />W1W 5PF</p>
                            <p>020 7088 8143</p>
                        </div>
                        <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1bf3c-60f1bedf" class="footer-div is-base">
                            <div class="body-large">South West</div>
                            <p>4th Floor, Granville Chambers,<br />21 Richmond Hill, Bournemouth,<br />BH2 6BJ</p>
                            <p>01202 971759</p>
                        </div>
                        <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1bf47-60f1bedf" class="footer-div is-base">
                            <div class="body-large">Our Newsletter</div>
                            <p>Join our mailing list to get sent regular updates.</p>
                            <div class="form-block w-form">
                                <form id="wf-form-Newsletter-Form" name="wf-form-Newsletter-Form"
                                    data-name="Newsletter Form" redirect="/thanks-for-signing-up"
                                    data-redirect="/thanks-for-signing-up"
                                    action="https://thinreelmedia.us13.list-manage.com/subscribe/post?u=3f66f4f931c267320fb60e38d&amp;amp;id=585ab4620c&amp;amp;f_id=00d72bebf0"
                                    method="post" class="form" data-wf-page-id="681062e6060f2b85468c82cd"
                                    data-wf-element-id="c7149a37-fd4c-3c63-3fef-8af7c91c2efa"><input
                                        class="form-field w-input" maxlength="256" name="EMAIL" data-name="EMAIL"
                                        placeholder="Your email address..." type="email" id="EMAIL-2" required="" />
                                    <div class="button-container"><input type="submit" data-wait=""
                                            class="submit-button w-button" value="" />
                                        <div class="primary-button is-news"><svg xmlns="http://www.w3.org/2000/svg"
                                                width="100%" viewBox="0 0 20 20" fill="none"
                                                class="button-arrow is-news">
                                                <g clip-path="url(#clip0_234_3928)">
                                                    <path
                                                        d="M13.16 7.47L10.49 7.56L13.64 10.73C14.38 11.47 15.05 12.13 15.66 12.72L2.77 12.68L7.5 0H5.5L0 14.76H0.84L15.57 14.81C14.95 15.41 14.26 16.08 13.52 16.83L10.45 19.92L13.22 20L19.42 13.76L13.17 7.47H13.16Z"
                                                        fill="currentColor"></path>
                                                </g>
                                                <defs>
                                                    <clippath id="clip0_234_3928">
                                                        <rect width="19.41" height="20" fill="currentColor"></rect>
                                                    </clippath>
                                                </defs>
                                            </svg>
                                            <p class="underline-link">Sign up</p>
                                        </div>
                                    </div>
                                </form>
                                <div class="success-div w-form-done">
                                    <div class="text-block">Thank you! You&#x27;re on the list!</div>
                                </div>
                                <div class="error-message w-form-fail">
                                    <div>Oops! Something went wrong while submitting the form.</div>
                                </div>
                            </div>
                        </div>
                        <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1bf4c-60f1bedf" class="footer-base">
                            <p>© Thin Reel Media Ltd 2025</p>
                        </div>
                        <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1bf4f-60f1bedf" class="footer-base is-email">
                            <div class="email-link is-highlight">Email us:</div><a href="mailto:<EMAIL>"
                                class="w-inline-block">
                                <p class="email-link is-email"><EMAIL></p>
                            </a>
                        </div>
                        <div id="w-node-_42ebee75-3fcc-d6a4-4219-337660f1bf53-60f1bedf" class="div-block-9 is-sf">
                            <div class="social-flex no-mobile"><a aria-label="Instagram - opens in new tab"
                                    href="https://www.instagram.com/thinreel/" target="_blank"
                                    class="social-div w-inline-block"><svg xmlns="http://www.w3.org/2000/svg"
                                        width="100%" viewBox="0 0 24 24" fill="none" class="social-icon">
                                        <path
                                            d="M21.94 7.88C21.9206 7.0503 21.7652 6.2294 21.48 5.45C21.2283 4.78181 20.8322 4.17742 20.32 3.68C19.8226 3.16776 19.2182 2.77166 18.55 2.52C17.7706 2.23484 16.9497 2.07945 16.12 2.06C15.06 2 14.72 2 12 2C9.28 2 8.94 2 7.88 2.06C7.0503 2.07945 6.2294 2.23484 5.45 2.52C4.78181 2.77166 4.17742 3.16776 3.68 3.68C3.16743 4.17518 2.77418 4.78044 2.53 5.45C2.23616 6.22734 2.07721 7.04915 2.06 7.88C2 8.94 2 9.28 2 12C2 14.72 2 15.06 2.06 16.12C2.07721 16.9508 2.23616 17.7727 2.53 18.55C2.77418 19.2196 3.16743 19.8248 3.68 20.32C4.17742 20.8322 4.78181 21.2283 5.45 21.48C6.2294 21.7652 7.0503 21.9206 7.88 21.94C8.94 22 9.28 22 12 22C14.72 22 15.06 22 16.12 21.94C16.9497 21.9206 17.7706 21.7652 18.55 21.48C19.2134 21.219 19.816 20.8242 20.3201 20.3201C20.8242 19.816 21.219 19.2134 21.48 18.55C21.7652 17.7706 21.9206 16.9497 21.94 16.12C21.94 15.06 22 14.72 22 12C22 9.28 22 8.94 21.94 7.88ZM20.14 16C20.1327 16.6348 20.0178 17.2637 19.8 17.86C19.6327 18.2913 19.3773 18.683 19.0501 19.0101C18.723 19.3373 18.3313 19.5927 17.9 19.76C17.3037 19.9778 16.6748 20.0927 16.04 20.1C15.04 20.15 14.67 20.16 12.04 20.16C9.41 20.16 9.04 20.16 8.04 20.1C7.38073 20.1148 6.72401 20.0132 6.1 19.8C5.66869 19.6327 5.27698 19.3773 4.94985 19.0501C4.62272 18.723 4.36734 18.3313 4.2 17.9C3.97775 17.2911 3.86271 16.6482 3.86 16C3.86 15 3.8 14.63 3.8 12C3.8 9.37 3.8 9 3.86 8C3.86271 7.35178 3.97775 6.70893 4.2 6.1C4.36734 5.66869 4.62272 5.27698 4.94985 4.94985C5.27698 4.62272 5.66869 4.36734 6.1 4.2C6.70893 3.97775 7.35178 3.86271 8 3.86C9 3.86 9.37 3.8 12 3.8C14.63 3.8 15 3.8 16 3.86C16.6348 3.86728 17.2637 3.98225 17.86 4.2C18.2913 4.36734 18.683 4.62272 19.0101 4.94985C19.3373 5.27698 19.5927 5.66869 19.76 6.1C19.9959 6.7065 20.1245 7.34942 20.14 8C20.19 9 20.2 9.37 20.2 12C20.2 14.63 20.19 15 20.14 16Z"
                                            fill="currentColor"></path>
                                        <path
                                            d="M12 6.86C10.9834 6.86 9.98964 7.16146 9.14437 7.72625C8.2991 8.29104 7.64029 9.0938 7.25126 10.033C6.86222 10.9722 6.76044 12.0057 6.95876 13.0028C7.15709 13.9998 7.64663 14.9157 8.36547 15.6345C9.08431 16.3534 10.0002 16.8429 10.9972 17.0412C11.9943 17.2396 13.0278 17.1378 13.967 16.7487C14.9062 16.3597 15.709 15.7009 16.2738 14.8556C16.8385 14.0104 17.14 13.0166 17.14 12C17.14 10.6368 16.5985 9.32941 15.6345 8.36547C14.6706 7.40153 13.3632 6.86 12 6.86ZM12 15.33C11.3414 15.33 10.6976 15.1347 10.15 14.7688C9.60234 14.4029 9.17552 13.8828 8.92348 13.2743C8.67144 12.6659 8.6055 11.9963 8.73399 11.3503C8.86247 10.7044 9.17963 10.111 9.64533 9.64533C10.111 9.17963 10.7044 8.86247 11.3503 8.73399C11.9963 8.6055 12.6659 8.67144 13.2743 8.92348C13.8828 9.17552 14.4029 9.60234 14.7688 10.15C15.1347 10.6976 15.33 11.3414 15.33 12C15.33 12.4373 15.2439 12.8703 15.0765 13.2743C14.9092 13.6784 14.6639 14.0454 14.3547 14.3547C14.0454 14.6639 13.6784 14.9092 13.2743 15.0765C12.8703 15.2439 12.4373 15.33 12 15.33Z"
                                            fill="currentColor"></path>
                                        <path
                                            d="M17.34 5.46001C17.1027 5.46001 16.8707 5.53039 16.6733 5.66224C16.476 5.7941 16.3222 5.98152 16.2313 6.20079C16.1405 6.42006 16.1168 6.66134 16.1631 6.89411C16.2094 7.12689 16.3236 7.34071 16.4915 7.50853C16.6593 7.67636 16.8731 7.79065 17.1059 7.83695C17.3387 7.88325 17.5799 7.85949 17.7992 7.76866C18.0185 7.67784 18.2059 7.52403 18.3378 7.32669C18.4696 7.12935 18.54 6.89734 18.54 6.66001C18.54 6.34175 18.4136 6.03652 18.1885 5.81148C17.9635 5.58643 17.6583 5.46001 17.34 5.46001Z"
                                            fill="currentColor"></path>
                                    </svg></a><a aria-label="LinkedIn - opens in new tab"
                                    href="https://www.linkedin.com/company/thin-reel-media-ltd/" target="_blank"
                                    class="social-div w-inline-block"><svg xmlns="http://www.w3.org/2000/svg"
                                        width="100%" viewBox="0 0 24 24" fill="none" class="social-icon">
                                        <path
                                            d="M20.9 20.9H17.166V15.053C17.166 13.659 17.138 11.865 15.222 11.865C13.277 11.865 12.98 13.382 12.98 14.95V20.9H9.249V8.87699H12.833V10.516H12.881C13.2402 9.90278 13.7588 9.39838 14.3818 9.05643C15.0048 8.71447 15.7088 8.54775 16.419 8.57399C20.199 8.57399 20.898 11.062 20.898 14.3V20.9H20.9ZM5.036 7.23199C4.60732 7.23259 4.1881 7.10603 3.83137 6.86832C3.47463 6.63061 3.19641 6.29244 3.03191 5.89658C2.8674 5.50072 2.824 5.06497 2.9072 4.64444C2.99039 4.22392 3.19644 3.83751 3.49928 3.53411C3.80212 3.23071 4.18815 3.02395 4.60852 2.93998C5.02889 2.85601 5.46473 2.8986 5.86089 3.06237C6.25705 3.22615 6.59573 3.50374 6.8341 3.86003C7.07246 4.21633 7.1998 4.63532 7.2 5.06399C7.20039 5.34847 7.14472 5.63024 7.03615 5.89319C6.92759 6.15615 6.76827 6.39512 6.5673 6.59647C6.36633 6.79781 6.12764 6.95757 5.86489 7.06662C5.60214 7.17567 5.32048 7.23186 5.036 7.23199ZM6.906 20.9H3.165V8.87699H6.906V20.9Z"
                                            fill="currentColor"></path>
                                    </svg></a></div>
                            <div class="sfco">
                                <p>Made by</p><a href="https://www.somefolk.co/" target="_blank" class="w-inline-block">
                                    <p class="para-link">SF.CO</p>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="footer-overlay"></div>
                </div>
            </section>
        </div>
    </div>
    <div class="showreel-wrapper">
        <div data-w-id="1b130c7b-46a1-a6fa-35e2-06354477fb99" class="showreel-block"></div>
        <div data-w-id="0e225ac3-cb25-dd67-e191-8dc67c1864cc" class="showreel-block"></div>
        <div data-w-id="8d1765d7-d8f2-0b82-492a-21176f7cf41d" class="showreel-block"></div>
        <div class="showreel-container">
            <div data-w-id="bd49ebd5-ebb0-2fa8-20be-752ef165e5e2" style="opacity:0" class="close-modal">
                <div class="close-active">
                    <div>Close</div>
                </div>
            </div>
            <div style="opacity:0" class="code-embed w-embed w-iframe">
                <div data-video-id="2589" style="aspect-ratio: 1.77777778; overflow: hidden;">

                    <iframe width="100%" height="100%"
                        src="https://app.vidzflow.com/v/EOB2vZmqmZ?dq=1080&ap=false&muted=false&loop=false&ctp=true&bv=false&piv=false&playsinline=false&bc=%23f45725&controls=play-large%2Cplay%2Cprogress%2Ccurrent-time%2Cmute%2Cvolume%2Csettings%2Cfullscreen"
                        title="Showreel Thin Reel 2023" style="aspect-ratio: 1.77777778; overflow: hidden;"
                        frameborder="0" scrolling="no" allow="fullscreen"></iframe>
                </div>
            </div>
        </div>
    </div>
    <div data-modal-group-status="not-active" class="modal">
        <div data-hover="" data-modal-close="" class="modal__dark"></div>
        <div data-modal-status="not-active" data-modal-name="modal-a" class="modal__card">
            <div class="modal__scroll">
                <div class="modal__content">
                    <div class="div-block-17">
                        <div data-modal-close="" class="div-block-18">
                            <div class="body-large is-dark-text">Close</div>
                        </div>
                        <div class="heading-3 centre-align is-dark-text is-form">Have a possible project?</div>
                    </div>
                    <div class="form-div w-embed w-script">
                        <div formsappId="6820ff26653b9b956efd8235"></div>
                        <script src="https://forms.app/cdn/embed.js" type="text/javascript" async defer
                            onload="new formsapp('6820ff26653b9b956efd8235', 'standard', {'width':'100vw','height':'82vh'}, 'https://oxr1mfvz.forms.app');"></script>
                    </div>
                </div>
            </div>
        </div>
        <div data-modal-status="not-active" data-modal-name="modal-b" class="modal__card">
            <div class="modal__scroll">
                <div class="modal__content">
                    <div class="div-block-17">
                        <div data-modal-close="" class="div-block-18">
                            <div class="body-large is-dark-text">Close</div>
                        </div>
                        <div class="heading-3 centre-align is-dark-text is-form">Need help on a pitch?</div>
                    </div>
                    <div class="form-div w-embed w-script">
                        <div formsappId="6820fd0fc9d500000289dcf4"></div>
                        <script src="https://forms.app/cdn/embed.js" type="text/javascript" async defer
                            onload="new formsapp('6820fd0fc9d500000289dcf4', 'standard', {'width':'100vw','height':'82vh'}, 'https://oxr1mfvz.forms.app');"></script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div data-w-id="2a6b4d60-b7fa-6576-a985-4098feb688d9" class="loader-div">
        <div data-w-id="c0f16a1d-4687-5299-31f2-01d4dee22055" class="loader-column"></div>
        <div data-w-id="71472697-f93c-d6bd-7fff-4571adf3f3d0" class="loader-column"></div>
        <div data-w-id="e0b5e1ee-8a30-5572-eb24-b140d48de301" class="loader-column"></div>
        <div data-w-id="683122a2-4023-0dd1-0d65-74d285675c1a" class="loader-column"></div>
        <div data-w-id="c47e9c88-ec43-9802-4ba7-f57af886c71a" class="loader-column no-mob"></div>
        <div data-w-id="3b09c472-5683-8853-54ed-cafd77431b13" class="loader-column no-mob"></div>
        <div class="logo-div">
            <div data-w-id="2111e75e-2f99-e9f2-8251-76b99183410b" data-is-ix2-target="1" class="lottie-animation-4"
                data-animation-type="lottie"
                data-src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/6827f45d9f8dc32554efd373_Logo_Animation_01_01.json"
                data-loop="0" data-direction="1" data-autoplay="0" data-renderer="svg" data-default-duration="0"
                data-duration="4"></div>
        </div>
    </div>
    <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=681062e6060f2b85468c82cf"
        type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
        crossorigin="anonymous"></script>
    <script src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/js/webflow.schunk.36b8fb49256177c8.js"
        type="text/javascript"></script>
    <script src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/js/webflow.schunk.82f44582d86d1ea9.js"
        type="text/javascript"></script>
    <script src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/js/webflow.schunk.1991d7c81cbee87c.js"
        type="text/javascript"></script>
    <script src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/js/webflow.schunk.dc2cb6388414c317.js"
        type="text/javascript"></script>
    <script src="https://cdn.prod.website-files.com/681062e6060f2b85468c82cf/js/webflow.b22da397.bc4060eec27991b0.js"
        type="text/javascript"></script>
    <script src="https://cdn.prod.website-files.com/gsap/3.13.0/gsap.min.js" type="text/javascript"></script>
    <script src="https://cdn.prod.website-files.com/gsap/3.13.0/Observer.min.js" type="text/javascript"></script>
    <script type="text/javascript">gsap.registerPlugin(Observer);</script>
    <script src="https://cdn.jsdelivr.net/gh/studio-freight/lenis@v0.2.26/bundled/lenis.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/kujira22/kujira_webgl@main/cw/sf24-1/gsap/1/main.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Remove any desktop-only videos on mobile
            if (window.innerWidth < 992) {
                document.querySelectorAll('.desktop-only-video').forEach(el => el.remove());
            }

            // Set up global listener for all Vidzflow videos
            window.addEventListener('message', function (e) {
                if (!e.origin.includes("vidzflow")) return;
                if (e.data.eventType !== 'videoReady') return;

                const videoId = e.data.videoId;
                const target = document.querySelector(`div[data-video-id="${videoId}"]`);

                if (target) {
                    const observer = new IntersectionObserver((entries, obs) => {
                        entries.forEach(entry => {
                            if (
                                entry.isIntersecting &&
                                entry.target.getAttribute('data-video-id') === String(videoId)
                            ) {
                                const iframe = entry.target.querySelector('iframe');
                                if (iframe) {
                                    iframe.contentWindow.postMessage("playerPlay", "*");
                                    obs.unobserve(entry.target);
                                }
                            }
                        });
                    });

                    observer.observe(target);
                }
            });
        });
    </script>

    <script>
        // Number of clicks on the trigger element initially set on 0
        let numOfClicks = 0;
        // Get trigger element 
        const trigger = document.getElementById("menu-trigger");
        // Get body element
        const bodyEl = document.getElementsByTagName("body")[0];

        // Set onclick function to trigger element
        trigger.onclick = () => {
            numOfClicks += 1;
            // Check if number of clicks is an even value: 
            // odd value - first click, even value - second click
            const isNumOfClicksEven = numOfClicks % 2 === 0;
            // On first click set body's overflow property to "auto", 
            // On second click set body's overflow property to "hidden"
            isNumOfClicksEven ? bodyEl.style.overflow = "auto" : bodyEl.style.overflow = "hidden";
        };
    </script>

    <script>

        function initTextAnimations() {

            const splitElements = document.querySelectorAll("[split-text]");

            splitElements.forEach((element) => {
                // Prevent running `SplitText` multiple times
                if (element.dataset.split === "true") return;

                // Mark element as split so it doesn't re-run
                element.dataset.split = "true";

                // Apply SplitText
                const split = new SplitText(element, {
                    type: "words,chars",
                    charsClass: "char",
                    wordsClass: "word"
                });

                // LETTERS Slide-up animation
                const tl = gsap.timeline({ paused: true });
                tl.from(split.chars, {
                    yPercent: 118,
                    duration: 1.3,
                    ease: "power4.inOut",
                    delay: 0.1,
                    stagger: { amount: 0.2 }
                });

                // Play animation immediately if needed
                if (element.getAttribute("data-name") === "up-we-go") {
                    tl.play();
                } else {
                    ScrollTrigger.create({
                        trigger: element,
                        start: "top bottom",
                        onEnter: () => tl.play()
                    });
                }

                // Ensure text is visible
                gsap.set(element, { opacity: 1 });
            });

        }


        window.addEventListener("DOMContentLoaded", initTextAnimations);

    </script>

    <script>

        function initCSSMarquee() {
            const pixelsPerSecond = 100; // Set the marquee speed (pixels per second)
            const marquees = document.querySelectorAll('[data-css-marquee]');

            // Duplicate each [data-css-marquee-list] element inside its container
            marquees.forEach(marquee => {
                marquee.querySelectorAll('[data-css-marquee-list]').forEach(list => {
                    const duplicate = list.cloneNode(true);
                    marquee.appendChild(duplicate);
                });
            });

            // Create an IntersectionObserver to check if the marquee container is in view
            const observer = new IntersectionObserver(entries => {
                entries.forEach(entry => {
                    entry.target.querySelectorAll('[data-css-marquee-list]').forEach(list =>
                        list.style.animationPlayState = entry.isIntersecting ? 'running' : 'paused'
                    );
                });
            }, { threshold: 0 });

            // Calculate the width and set the animation duration accordingly
            marquees.forEach(marquee => {
                marquee.querySelectorAll('[data-css-marquee-list]').forEach(list => {
                    list.style.animationDuration = (list.offsetWidth / pixelsPerSecond) + 's';
                    list.style.animationPlayState = 'paused';
                });
                observer.observe(marquee);
            });
        }

        // Initialize CSS Marquee
        document.addEventListener('DOMContentLoaded', function () {
            initCSSMarquee();
        });

    </script>


    <script>
        function initModalBasic() {

            const modalGroup = document.querySelector('[data-modal-group-status]');
            const modals = document.querySelectorAll('[data-modal-name]');
            const modalTargets = document.querySelectorAll('[data-modal-target]');

            // Open modal
            modalTargets.forEach((modalTarget) => {
                modalTarget.addEventListener('click', function () {
                    const modalTargetName = this.getAttribute('data-modal-target');

                    // Close all modals
                    modalTargets.forEach((target) => target.setAttribute('data-modal-status', 'not-active'));
                    modals.forEach((modal) => modal.setAttribute('data-modal-status', 'not-active'));

                    // Activate clicked modal
                    document.querySelector(`[data-modal-target="${modalTargetName}"]`).setAttribute('data-modal-status', 'active');
                    document.querySelector(`[data-modal-name="${modalTargetName}"]`).setAttribute('data-modal-status', 'active');

                    // Set group to active
                    if (modalGroup) {
                        modalGroup.setAttribute('data-modal-group-status', 'active');
                    }
                });
            });

            // Close modal
            document.querySelectorAll('[data-modal-close]').forEach((closeBtn) => {
                closeBtn.addEventListener('click', closeAllModals);
            });

            // Close modal on `Escape` key
            document.addEventListener('keydown', function (event) {
                if (event.key === 'Escape') {
                    closeAllModals();
                }
            });

            // Function to close all modals
            function closeAllModals() {
                modalTargets.forEach((target) => target.setAttribute('data-modal-status', 'not-active'));

                if (modalGroup) {
                    modalGroup.setAttribute('data-modal-group-status', 'not-active');
                }
            }
        }

        // Initialize Basic Modal
        document.addEventListener('DOMContentLoaded', () => {
            initModalBasic();
        });
    </script>





    <audio id="blog-music" loop preload="auto"
        src="https://od.lk/s/NV8yMDMyMzAwNDJf/Mogli%20the%20Iceburg%20-%20You%20Cant%20Hold%20Me%20Down%20-%20No%20Lead%20Vocals.mp3"></audio>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            setupBlogMusic();
        });

        // Also run this again after a transition (optional, if you're using custom transitions or Swup later)
        window.addEventListener('pageshow', () => {
            setupBlogMusic();
        });

        function setupBlogMusic() {
            const audio = document.getElementById('blog-music');
            const toggle = document.getElementById('toggle-music');
            const isBlogPage = document.body.classList.contains('blog') || document.body.classList.contains('blog-post');

            if (!isBlogPage || !audio || !toggle) return;

            // Remove existing click handlers just in case (avoid double-binding)
            toggle.replaceWith(toggle.cloneNode(true));
            const newToggle = document.getElementById('toggle-music');

            // Update button state
            const musicState = sessionStorage.getItem('blog-music-on');
            if (musicState === 'true') {
                try {
                    audio.play();
                    newToggle.textContent = 'Sound On';
                } catch (e) {
                    console.warn('Could not auto-play audio:', e);
                }
            } else {
                newToggle.textContent = 'Sound Off';
            }

            newToggle.addEventListener('click', () => {
                if (audio.paused) {
                    audio.volume = 0.5;
                    audio.play().then(() => {
                        newToggle.textContent = 'Sound On';
                        sessionStorage.setItem('blog-music-on', 'true');
                    }).catch(err => {
                        console.warn('Playback error:', err);
                    });
                } else {
                    audio.pause();
                    newToggle.textContent = 'Sound Off';
                    sessionStorage.setItem('blog-music-on', 'false');
                }
            });
        }
    </script>

    <script>
        // Only run on the homepage
        if (window.location.pathname === '/') {
            // Scroll to top on first page load
            window.scrollTo(0, 0);

            // Scroll to top again after back/forward navigation
            window.addEventListener('pageshow', function () {
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 10); // short delay helps with Safari
            });

            // Extra insurance for Webflow-rendered content
            window.addEventListener('load', function () {
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 10);
            });
        }
    </script>


    <script>
        function initAccordionCSS() {
            document.querySelectorAll('[data-accordion-css-init]').forEach((accordion) => {
                const closeSiblings = accordion.getAttribute('data-accordion-close-siblings') === 'true';

                accordion.addEventListener('click', (event) => {
                    const toggle = event.target.closest('[data-accordion-toggle]');
                    if (!toggle) return; // Exit if the clicked element is not a toggle

                    const singleAccordion = toggle.closest('[data-accordion-status]');
                    if (!singleAccordion) return; // Exit if no accordion container is found

                    const isActive = singleAccordion.getAttribute('data-accordion-status') === 'active';
                    singleAccordion.setAttribute('data-accordion-status', isActive ? 'not-active' : 'active');

                    // When [data-accordion-close-siblings="true"]
                    if (closeSiblings && !isActive) {
                        accordion.querySelectorAll('[data-accordion-status="active"]').forEach((sibling) => {
                            if (sibling !== singleAccordion) sibling.setAttribute('data-accordion-status', 'not-active');
                        });
                    }
                });
            });
        }

        // Initialize Accordion CSS
        document.addEventListener('DOMContentLoaded', () => {
            initAccordionCSS();
        });
    </script>

    <script>
        function initDynamicCustomTextCursor() {
            let cursorItem = document.querySelector(".cursor");
            let cursorParagraph = cursorItem.querySelector("p");
            let targets = document.querySelectorAll("[data-cursor]");
            let xOffset = 6;
            let yOffset = 140;
            let cursorIsOnRight = false;
            let currentTarget = null;
            let lastText = '';

            // Position cursor relative to actual cursor position on page load
            gsap.set(cursorItem, { xPercent: xOffset, yPercent: yOffset });

            // Use GSAP quick.to for a more performative tween on the cursor
            let xTo = gsap.quickTo(cursorItem, "x", { ease: "power3" });
            let yTo = gsap.quickTo(cursorItem, "y", { ease: "power3" });

            // Function to get the width of the cursor element including a buffer
            const getCursorEdgeThreshold = () => {
                return cursorItem.offsetWidth + 16; // Cursor width + 16px margin
            };

            // On mousemove, call the quickTo functions to the actual cursor position
            window.addEventListener("mousemove", e => {
                let windowWidth = window.innerWidth;
                let windowHeight = window.innerHeight;
                let scrollY = window.scrollY;
                let cursorX = e.clientX;
                let cursorY = e.clientY + scrollY; // Adjust cursorY to account for scroll

                // Default offsets
                let xPercent = xOffset;
                let yPercent = yOffset;

                // Adjust X offset dynamically based on cursor width
                let cursorEdgeThreshold = getCursorEdgeThreshold();
                if (cursorX > windowWidth - cursorEdgeThreshold) {
                    cursorIsOnRight = true;
                    xPercent = -100;
                } else {
                    cursorIsOnRight = false;
                }

                // Adjust Y offset if in the bottom 10% of the current viewport
                if (cursorY > scrollY + windowHeight * 0.9) {
                    yPercent = -120;
                }

                if (currentTarget) {
                    let newText = currentTarget.getAttribute("data-cursor");
                    if (newText !== lastText) { // Only update if the text is different
                        cursorParagraph.innerHTML = newText;
                        lastText = newText;

                        // Recalculate edge awareness whenever the text changes
                        cursorEdgeThreshold = getCursorEdgeThreshold();
                    }
                }

                gsap.to(cursorItem, { xPercent: xPercent, yPercent: yPercent, duration: 0.9, ease: "power3" });
                xTo(cursorX);
                yTo(cursorY - scrollY);
            });

            // Add a mouse enter listener for each link that has a data-cursor attribute
            targets.forEach(target => {
                target.addEventListener("mouseenter", () => {
                    currentTarget = target; // Set the current target

                    let newText = target.getAttribute("data-cursor");

                    // Update only if the text changes
                    if (newText !== lastText) {
                        cursorParagraph.innerHTML = newText;
                        lastText = newText;

                        // Recalculate edge awareness whenever the text changes
                        let cursorEdgeThreshold = getCursorEdgeThreshold();
                    }
                });
            });
        }

        // Initialize Dynamic Text Cursor (Edge Aware)
        document.addEventListener('DOMContentLoaded', () => {
            initDynamicCustomTextCursor();
        });
    </script>
</body>

</html>