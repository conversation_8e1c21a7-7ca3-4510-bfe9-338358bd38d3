<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Name | Master of Design</title>
    <meta name="description" content="Creative designer and visual storyteller. Crafting exceptional digital experiences through innovative design and creative vision.">
    <meta name="keywords" content="designer, portfolio, creative, branding, web design, UI/UX, visual design, creative direction">
    <meta name="author" content="Your Name">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Your Name | Master of Design">
    <meta property="og:description" content="Creative designer and visual storyteller. Crafting exceptional digital experiences through innovative design and creative vision.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://yourportfolio.com">
    <meta property="og:image" content="https://yourportfolio.com/assets/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Your Name | Master of Design">
    <meta name="twitter:description" content="Creative designer and visual storyteller. Crafting exceptional digital experiences through innovative design and creative vision.">
    <meta name="twitter:image" content="https://yourportfolio.com/assets/og-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Preload critical resources -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="script.js" as="script">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="loading">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Loading Screen -->
    <div class="loader">
        <div class="loader-content">
            <div class="loader-text">
                <span class="loader-letter">L</span>
                <span class="loader-letter">O</span>
                <span class="loader-letter">A</span>
                <span class="loader-letter">D</span>
                <span class="loader-letter">I</span>
                <span class="loader-letter">N</span>
                <span class="loader-letter">G</span>
            </div>
            <div class="loader-progress">
                <div class="loader-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navigation" id="navigation">
        <div class="scroll-progress" id="scroll-progress"></div>
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#home" class="logo-link">
                    <span class="logo-text">YourName</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#home" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="#about" class="nav-link">About</a>
                    </li>
                    <li class="nav-item">
                        <a href="#portfolio" class="nav-link">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a href="#services" class="nav-link">Services</a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">Contact</a>
                    </li>
                </ul>
            </div>

            <div class="nav-toggle" id="nav-toggle">
                <span class="nav-toggle-line"></span>
                <span class="nav-toggle-line"></span>
                <span class="nav-toggle-line"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="main-content">
        <!-- Hero Section -->
        <section class="hero" id="home">
            <div class="hero-background">
                <div class="hero-video-container">
                    <video class="hero-video" autoplay muted loop playsinline>
                        <source src="assets/hero-video.mp4" type="video/mp4">
                    </video>
                    <div class="hero-overlay"></div>
                </div>
                <div class="hero-particles" id="hero-particles"></div>
            </div>
            
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="hero-title-line">Master of</span>
                        <span class="hero-title-line hero-title-accent">Design</span>
                    </h1>
                    <p class="hero-subtitle">
                        Creative designer and visual storyteller crafting exceptional digital experiences through innovative design and creative vision.
                    </p>
                    <div class="hero-cta">
                        <a href="#portfolio" class="btn btn-primary">View My Work</a>
                        <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                    </div>
                </div>
                
                <div class="hero-scroll">
                    <div class="scroll-indicator">
                        <span class="scroll-text">Scroll</span>
                        <div class="scroll-line"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about" id="about">
            <div class="container">
                <div class="about-content">
                    <div class="about-text">
                        <h2 class="section-title">About Me</h2>
                        <p class="about-description">
                            I'm a passionate designer with over 5 years of experience creating compelling visual narratives and digital experiences. My work spans across branding, web design, and creative direction.
                        </p>
                        <div class="about-stats">
                            <div class="stat-item">
                                <span class="stat-number">50+</span>
                                <span class="stat-label">Projects Completed</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">25+</span>
                                <span class="stat-label">Happy Clients</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">5+</span>
                                <span class="stat-label">Years Experience</span>
                            </div>
                        </div>
                    </div>
                    <div class="about-image">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop" alt="About Me" class="about-img">
                    </div>
                </div>

                <!-- Skills Section -->
                <div class="skills-section">
                    <h3 class="skills-title">Skills & Expertise</h3>
                    <div class="skills-grid">
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">UI/UX Design</span>
                                <span class="skill-percentage">95%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="95"></div>
                            </div>
                        </div>

                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Brand Identity</span>
                                <span class="skill-percentage">90%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90"></div>
                            </div>
                        </div>

                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Web Development</span>
                                <span class="skill-percentage">85%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85"></div>
                            </div>
                        </div>

                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Creative Direction</span>
                                <span class="skill-percentage">88%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="88"></div>
                            </div>
                        </div>

                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Motion Graphics</span>
                                <span class="skill-percentage">80%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="80"></div>
                            </div>
                        </div>

                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Photography</span>
                                <span class="skill-percentage">75%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="75"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Portfolio Section -->
        <section class="portfolio" id="portfolio">
            <div class="container">
                <h2 class="section-title">Featured Work</h2>
                <div class="portfolio-filters" id="portfolio-filters">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="branding">Branding</button>
                    <button class="filter-btn" data-filter="web-design">Web Design</button>
                    <button class="filter-btn" data-filter="ui-ux">UI/UX</button>
                    <button class="filter-btn" data-filter="creative-direction">Creative Direction</button>
                </div>
                <div class="portfolio-grid" id="portfolio-grid">
                    <!-- Portfolio items will be dynamically loaded -->
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="services" id="services">
            <div class="container">
                <h2 class="section-title">Services</h2>
                <div class="services-grid">
                    <div class="service-item">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <circle cx="8.5" cy="8.5" r="1.5"/>
                                <polyline points="21,15 16,10 5,21"/>
                            </svg>
                        </div>
                        <h3 class="service-title">Brand Design</h3>
                        <p class="service-description">Creating memorable brand identities that resonate with your audience.</p>
                    </div>
                    
                    <div class="service-item">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                <line x1="8" y1="21" x2="16" y2="21"/>
                                <line x1="12" y1="17" x2="12" y2="21"/>
                            </svg>
                        </div>
                        <h3 class="service-title">Web Design</h3>
                        <p class="service-description">Designing beautiful and functional websites that convert.</p>
                    </div>
                    
                    <div class="service-item">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
                            </svg>
                        </div>
                        <h3 class="service-title">Creative Direction</h3>
                        <p class="service-description">Guiding creative projects from concept to completion.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact" id="contact">
            <div class="container">
                <div class="contact-content">
                    <div class="contact-info">
                        <h2 class="section-title">Let's Work Together</h2>
                        <p class="contact-description">
                            Ready to bring your vision to life? Let's discuss your next project and create something amazing together.
                        </p>
                        <div class="contact-details">
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="m22 2-7 20-4-9-9-4 20-7z"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <span class="contact-label">Email</span>
                                    <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                                </div>
                            </div>
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <span class="contact-label">Phone</span>
                                    <a href="tel:+1234567890" class="contact-link">+1 (234) 567-890</a>
                                </div>
                            </div>
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <span class="contact-label">Location</span>
                                    <span class="contact-link">New York, NY</span>
                                </div>
                            </div>
                        </div>

                        <div class="social-links">
                            <h4>Follow Me</h4>
                            <div class="social-icons">
                                <a href="#" class="social-icon" aria-label="Instagram">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="2" y="2" width="20" height="20" rx="5" ry="5"/>
                                        <path d="m16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/>
                                        <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-icon" aria-label="LinkedIn">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
                                        <rect x="2" y="9" width="4" height="12"/>
                                        <circle cx="4" cy="4" r="2"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-icon" aria-label="Behance">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M8 8h3a3 3 0 0 1 0 6H8V8z"/>
                                        <path d="M8 14h4a3 3 0 0 1 0 6H8v-6z"/>
                                        <path d="M16 8h4"/>
                                        <path d="M18 10a3 3 0 1 1 0 6"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-icon" aria-label="Dribbble">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="m8.56 2.75 4.37 6.03a10.02 10.02 0 0 0 4.91-8.77"/>
                                        <path d="m2.75 15.44 6.03-4.37a10.02 10.02 0 0 0-8.77-4.91"/>
                                        <path d="m21.25 8.56-6.03 4.37a10.02 10.02 0 0 0 8.77 4.91"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    <form class="contact-form" id="contact-form">
                        <div class="form-row">
                            <div class="form-group">
                                <input type="text" id="name" name="name" placeholder="Your Name" required>
                            </div>
                            <div class="form-group">
                                <input type="email" id="email" name="email" placeholder="Your Email" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <input type="text" id="subject" name="subject" placeholder="Subject" required>
                        </div>
                        <div class="form-group">
                            <textarea id="message" name="message" placeholder="Your Message" rows="6" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <span>Send Message</span>
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m22 2-7 20-4-9-9-4 20-7z"/>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p class="footer-text">&copy; 2024 Your Name. All rights reserved.</p>
                <div class="footer-social">
                    <a href="#" class="social-link">Instagram</a>
                    <a href="#" class="social-link">LinkedIn</a>
                    <a href="#" class="social-link">Behance</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Custom Cursor -->
    <div class="cursor" id="cursor">
        <div class="cursor-dot"></div>
        <div class="cursor-outline"></div>
    </div>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
