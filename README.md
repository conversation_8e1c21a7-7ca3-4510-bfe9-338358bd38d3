# Portfolio Website - Master of Design

A modern, responsive portfolio website inspired by professional design agencies like ThinReel and Farm League. Built with vanilla HTML, CSS, and JavaScript featuring smooth animations, interactive elements, and a mobile-first approach.

## Features

### 🎨 Design & Layout
- **Modern Hero Section** with animated background and particle effects
- **Responsive Grid Layouts** for portfolio, services, and skills
- **Professional Typography** using Inter and Playfair Display fonts
- **Smooth Animations** powered by GSAP and ScrollTrigger
- **Custom Cursor** for desktop interactions
- **Mobile-Optimized** touch interactions

### 🚀 Interactive Elements
- **Portfolio Filtering** by category with smooth transitions
- **Animated Navigation** with scroll progress indicator
- **Skill Progress Bars** with animated counters
- **Contact Form** with validation and feedback
- **Hover Effects** and micro-interactions throughout
- **Loading Screen** with animated text

### 📱 Responsive Design
- **Mobile-First** approach with breakpoints at 480px, 768px, 992px, 1312px, 1600px
- **Touch-Friendly** interactions for mobile devices
- **Optimized Performance** with lazy loading and efficient animations
- **Cross-Browser** compatibility

## File Structure

```
portfolio-website/
├── index.html          # Main HTML file
├── styles.css          # All CSS styles and responsive design
├── script.js           # JavaScript functionality and animations
├── assets/             # Media assets folder
│   └── placeholder.txt # Asset requirements guide
└── README.md           # This file
```

## Setup Instructions

1. **Clone or Download** the project files
2. **Add Your Assets** to the `assets/` folder:
   - `hero-video.mp4` - Background video for hero section
   - `about-image.jpg` - Your professional photo
   - `favicon.ico` - Website favicon
   - `apple-touch-icon.png` - Apple touch icon
   - `og-image.jpg` - Social sharing image

3. **Customize Content**:
   - Update personal information in `index.html`
   - Modify portfolio data in `script.js`
   - Adjust colors and fonts in CSS variables
   - Replace placeholder images with your work

4. **Launch** by opening `index.html` in a web browser

## Customization Guide

### Colors & Branding
Update CSS variables in `styles.css`:
```css
:root {
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #ff6b35;
    /* ... other variables */
}
```

### Portfolio Content
Edit the `portfolioData` array in `script.js`:
```javascript
const portfolioData = [
    {
        id: 1,
        title: "Your Project Title",
        category: "branding", // branding, web-design, ui-ux, creative-direction
        image: "path/to/your/image.jpg",
        description: "Project description",
        tags: ["Tag1", "Tag2", "Tag3"]
    },
    // ... more projects
];
```

### Personal Information
Update contact details and social links in `index.html`:
- Email address
- Phone number
- Location
- Social media links
- About section content

## Browser Support

- **Chrome** 60+
- **Firefox** 60+
- **Safari** 12+
- **Edge** 79+

## Performance Features

- **Lazy Loading** for images
- **Optimized Animations** with GSAP
- **Efficient CSS** with modern properties
- **Minimal JavaScript** for fast loading
- **Responsive Images** with WebP support

## Dependencies

- **GSAP** (loaded from CDN) - Animation library
- **Google Fonts** - Typography (Inter & Playfair Display)
- **Unsplash** - Placeholder images (replace with your own)

## License

This project is open source and available under the [MIT License](LICENSE).

## Credits

- Design inspiration from ThinReel and Farm League
- Icons from Feather Icons
- Placeholder images from Unsplash
- Fonts from Google Fonts

## Support

For questions or support, please contact [<EMAIL>]

---

**Built with ❤️ for creative professionals**
