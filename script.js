// Portfolio data
const portfolioData = [
    {
        id: 1,
        title: "Brand Identity Design",
        category: "branding",
        image: "https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=600&h=400&fit=crop",
        description: "Complete brand identity design for a tech startup",
        tags: ["Logo Design", "Brand Guidelines", "Visual Identity"]
    },
    {
        id: 2,
        title: "E-commerce Website",
        category: "web-design",
        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop",
        description: "Modern e-commerce platform with seamless user experience",
        tags: ["UI/UX Design", "E-commerce", "Responsive Design"]
    },
    {
        id: 3,
        title: "Mobile App Interface",
        category: "ui-ux",
        image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop",
        description: "Intuitive mobile app design for fitness tracking",
        tags: ["Mobile Design", "User Experience", "Prototyping"]
    },
    {
        id: 4,
        title: "Creative Campaign",
        category: "creative-direction",
        image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600&h=400&fit=crop",
        description: "Multi-channel creative campaign for fashion brand",
        tags: ["Creative Direction", "Campaign Design", "Visual Strategy"]
    },
    {
        id: 5,
        title: "Corporate Website",
        category: "web-design",
        image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=600&h=400&fit=crop",
        description: "Professional corporate website with modern aesthetics",
        tags: ["Corporate Design", "Web Development", "CMS"]
    },
    {
        id: 6,
        title: "Packaging Design",
        category: "branding",
        image: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&h=400&fit=crop",
        description: "Sustainable packaging design for organic products",
        tags: ["Packaging", "Sustainability", "Product Design"]
    },
    {
        id: 7,
        title: "Dashboard Design",
        category: "ui-ux",
        image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
        description: "Analytics dashboard with intuitive data visualization",
        tags: ["Dashboard", "Data Visualization", "UX Design"]
    },
    {
        id: 8,
        title: "Art Direction",
        category: "creative-direction",
        image: "https://images.unsplash.com/photo-1493612276216-ee3925520721?w=600&h=400&fit=crop",
        description: "Art direction for luxury fashion photoshoot",
        tags: ["Art Direction", "Photography", "Fashion"]
    }
];

// DOM Elements
const loader = document.querySelector('.loader');
const navigation = document.querySelector('.navigation');
const navToggle = document.querySelector('.nav-toggle');
const navMenu = document.querySelector('.nav-menu');
const navLinks = document.querySelectorAll('.nav-link');
const portfolioGrid = document.querySelector('#portfolio-grid');
const contactForm = document.querySelector('#contact-form');

// Initialize GSAP
gsap.registerPlugin(ScrollTrigger);

// Loading Animation
window.addEventListener('load', () => {
    setTimeout(() => {
        loader.classList.add('hidden');
        document.body.classList.remove('loading');
        initAnimations();
        initHeroVideo();
    }, 2000);
});

// Hero video initialization
function initHeroVideo() {
    const heroVideo = document.querySelector('.hero-video');

    if (heroVideo) {
        heroVideo.addEventListener('loadeddata', () => {
            heroVideo.classList.add('loaded');
        });

        // Fallback if video fails to load
        heroVideo.addEventListener('error', () => {
            console.log('Hero video failed to load, using gradient background');
            heroVideo.style.display = 'none';
        });

        // Try to load and play the video
        heroVideo.load();
    }

    // Initialize particles
    createParticles();
}

// Create floating particles
function createParticles() {
    const particlesContainer = document.querySelector('#hero-particles');
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // Random size between 2px and 8px
        const size = Math.random() * 6 + 2;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';

        // Random position
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';

        // Random animation delay
        particle.style.animationDelay = Math.random() * 6 + 's';

        particlesContainer.appendChild(particle);
    }
}

// Navigation functionality
navToggle.addEventListener('click', () => {
    navMenu.classList.toggle('active');
    navToggle.classList.toggle('active');
});

// Close mobile menu when clicking on links
navLinks.forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('active');
        navToggle.classList.remove('active');
    });
});

// Navigation scroll effect and progress bar
window.addEventListener('scroll', () => {
    // Navigation background
    if (window.scrollY > 100) {
        navigation.classList.add('scrolled');
    } else {
        navigation.classList.remove('scrolled');
    }

    // Scroll progress bar
    const scrollProgress = document.querySelector('#scroll-progress');
    const scrollTop = window.pageYOffset;
    const docHeight = document.body.offsetHeight;
    const winHeight = window.innerHeight;
    const scrollPercent = scrollTop / (docHeight - winHeight);
    const scrollPercentRounded = Math.round(scrollPercent * 100);

    if (scrollProgress) {
        scrollProgress.style.transform = `scaleX(${scrollPercent})`;
    }
});

// Smooth scrolling for navigation links
navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href');
        const targetSection = document.querySelector(targetId);
        
        if (targetSection) {
            const offsetTop = targetSection.offsetTop - 80;
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
    });
});

// Portfolio grid generation
function generatePortfolioGrid(filter = 'all') {
    portfolioGrid.innerHTML = '';

    const filteredData = filter === 'all'
        ? portfolioData
        : portfolioData.filter(item => item.category === filter);

    filteredData.forEach((item, index) => {
        const portfolioItem = document.createElement('div');
        portfolioItem.className = 'portfolio-item';
        portfolioItem.setAttribute('data-category', item.category);
        portfolioItem.innerHTML = `
            <div class="portfolio-image">
                <img src="${item.image}" alt="${item.title}" loading="lazy">
                <div class="portfolio-overlay">
                    <div class="portfolio-content">
                        <h3 class="portfolio-title">${item.title}</h3>
                        <p class="portfolio-description">${item.description}</p>
                        <div class="portfolio-tags">
                            ${item.tags.map(tag => `<span class="portfolio-tag">${tag}</span>`).join('')}
                        </div>
                        <a href="#" class="portfolio-link">View Project</a>
                    </div>
                </div>
            </div>
        `;

        portfolioGrid.appendChild(portfolioItem);
    });

    // Re-animate portfolio items
    setTimeout(() => {
        animatePortfolioItems();
        addPortfolioHoverEffects();
    }, 100);
}

// Portfolio filtering
function initPortfolioFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            button.classList.add('active');

            // Get filter value
            const filter = button.getAttribute('data-filter');

            // Animate out current items
            gsap.to('.portfolio-item', {
                duration: 0.3,
                opacity: 0,
                y: 20,
                stagger: 0.05,
                onComplete: () => {
                    // Generate new grid
                    generatePortfolioGrid(filter);

                    // Animate in new items
                    gsap.fromTo('.portfolio-item',
                        { opacity: 0, y: 20 },
                        {
                            duration: 0.6,
                            opacity: 1,
                            y: 0,
                            stagger: 0.1,
                            ease: 'power3.out'
                        }
                    );
                }
            });
        });
    });
}

// Contact form handling
contactForm.addEventListener('submit', (e) => {
    e.preventDefault();
    
    const formData = new FormData(contactForm);
    const name = formData.get('name');
    const email = formData.get('email');
    const message = formData.get('message');
    
    // Simulate form submission
    const submitButton = contactForm.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    
    submitButton.textContent = 'Sending...';
    submitButton.disabled = true;
    
    setTimeout(() => {
        alert('Thank you for your message! I\'ll get back to you soon.');
        contactForm.reset();
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }, 2000);
});

// GSAP Animations
function initAnimations() {
    // Hero animations
    gsap.timeline()
        .from('.hero-title-line', {
            duration: 1,
            y: 100,
            opacity: 0,
            stagger: 0.2,
            ease: 'power3.out'
        })
        .from('.hero-subtitle', {
            duration: 0.8,
            y: 50,
            opacity: 0,
            ease: 'power3.out'
        }, '-=0.5')
        .from('.hero-cta .btn', {
            duration: 0.6,
            y: 30,
            opacity: 0,
            stagger: 0.1,
            ease: 'power3.out'
        }, '-=0.3')
        .from('.hero-scroll', {
            duration: 0.6,
            opacity: 0,
            ease: 'power3.out'
        }, '-=0.2');

    // Section animations
    gsap.utils.toArray('.section-title').forEach(title => {
        gsap.from(title, {
            duration: 1,
            y: 50,
            opacity: 0,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: title,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            }
        });
    });

    // About section animations
    gsap.from('.about-description', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.about-description',
            start: 'top 80%'
        }
    });

    gsap.from('.stat-item', {
        duration: 0.6,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.about-stats',
            start: 'top 80%'
        }
    });

    gsap.from('.about-img', {
        duration: 1,
        scale: 0.8,
        opacity: 0,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.about-img',
            start: 'top 80%'
        }
    });

    // Services animations
    gsap.from('.service-item', {
        duration: 0.8,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.services-grid',
            start: 'top 80%'
        }
    });

    // Contact section animations
    gsap.from('.contact-info', {
        duration: 0.8,
        x: -50,
        opacity: 0,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.contact-content',
            start: 'top 80%'
        }
    });

    gsap.from('.contact-form', {
        duration: 0.8,
        x: 50,
        opacity: 0,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.contact-content',
            start: 'top 80%'
        }
    });

    // Portfolio items animation (will be called after portfolio is generated)
    animatePortfolioItems();
}

function animatePortfolioItems() {
    gsap.from('.portfolio-item', {
        duration: 0.8,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.portfolio-grid',
            start: 'top 80%'
        }
    });
}

// Parallax effect for hero video
gsap.to('.hero-video', {
    yPercent: -50,
    ease: 'none',
    scrollTrigger: {
        trigger: '.hero',
        start: 'top bottom',
        end: 'bottom top',
        scrub: true
    }
});

// Additional scroll animations
gsap.utils.toArray('.service-item').forEach((item, index) => {
    gsap.from(item, {
        duration: 0.8,
        y: 50,
        opacity: 0,
        delay: index * 0.1,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: item,
            start: 'top 85%',
            end: 'bottom 15%',
            toggleActions: 'play none none reverse'
        }
    });
});

// Stagger animation for contact items
gsap.from('.contact-item', {
    duration: 0.6,
    x: -30,
    opacity: 0,
    stagger: 0.2,
    ease: 'power3.out',
    scrollTrigger: {
        trigger: '.contact-details',
        start: 'top 80%'
    }
});

// Form animation
gsap.from('.contact-form', {
    duration: 0.8,
    x: 30,
    opacity: 0,
    ease: 'power3.out',
    scrollTrigger: {
        trigger: '.contact-form',
        start: 'top 80%'
    }
});

// Initialize portfolio
generatePortfolioGrid();
initPortfolioFilters();

// Custom Cursor
function initCustomCursor() {
    const cursor = document.querySelector('#cursor');
    const cursorDot = document.querySelector('.cursor-dot');
    const cursorOutline = document.querySelector('.cursor-outline');

    if (!cursor) return;

    let mouseX = 0;
    let mouseY = 0;
    let outlineX = 0;
    let outlineY = 0;

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;

        cursorDot.style.left = mouseX + 'px';
        cursorDot.style.top = mouseY + 'px';
    });

    // Smooth follow for outline
    function animateOutline() {
        outlineX += (mouseX - outlineX) * 0.1;
        outlineY += (mouseY - outlineY) * 0.1;

        cursorOutline.style.left = outlineX + 'px';
        cursorOutline.style.top = outlineY + 'px';

        requestAnimationFrame(animateOutline);
    }
    animateOutline();

    // Hover effects
    const hoverElements = document.querySelectorAll('a, button, .portfolio-item, .service-item, .filter-btn');

    hoverElements.forEach(el => {
        el.addEventListener('mouseenter', () => {
            cursor.classList.add('hover');
        });

        el.addEventListener('mouseleave', () => {
            cursor.classList.remove('hover');
        });
    });

    // Click effect
    document.addEventListener('mousedown', () => {
        cursor.classList.add('click');
    });

    document.addEventListener('mouseup', () => {
        cursor.classList.remove('click');
    });
}

// Initialize cursor on desktop only
if (window.innerWidth > 768) {
    initCustomCursor();
}

// Mobile touch interactions
function initMobileInteractions() {
    if (window.innerWidth <= 768) {
        // Add touch feedback for portfolio items
        const portfolioItems = document.querySelectorAll('.portfolio-item');

        portfolioItems.forEach(item => {
            item.addEventListener('touchstart', () => {
                item.style.transform = 'scale(0.98)';
            });

            item.addEventListener('touchend', () => {
                item.style.transform = 'scale(1)';
                // Show overlay on touch for mobile
                const overlay = item.querySelector('.portfolio-overlay');
                if (overlay) {
                    overlay.style.opacity = overlay.style.opacity === '1' ? '0' : '1';
                }
            });
        });

        // Smooth scroll behavior for mobile navigation
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('touchend', (e) => {
                // Add small delay to ensure menu closes before scrolling
                setTimeout(() => {
                    const targetId = link.getAttribute('href');
                    const targetSection = document.querySelector(targetId);

                    if (targetSection) {
                        const offsetTop = targetSection.offsetTop - 80;
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                }, 300);
            });
        });
    }
}

// Initialize mobile interactions
initMobileInteractions();

// Reinitialize on window resize
window.addEventListener('resize', () => {
    if (window.innerWidth <= 768) {
        initMobileInteractions();
    }
});

// Add portfolio item hover effects
function addPortfolioHoverEffects() {
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    portfolioItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            gsap.to(item.querySelector('.portfolio-overlay'), {
                duration: 0.3,
                opacity: 1,
                ease: 'power2.out'
            });
        });

        item.addEventListener('mouseleave', () => {
            gsap.to(item.querySelector('.portfolio-overlay'), {
                duration: 0.3,
                opacity: 0,
                ease: 'power2.out'
            });
        });
    });
}

// Intersection Observer for scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
        }
    });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.service-item, .portfolio-item, .stat-item').forEach(el => {
    observer.observe(el);
});

// Add scroll-triggered number counting animation
function animateNumbers() {
    const numbers = document.querySelectorAll('.stat-number');
    
    numbers.forEach(number => {
        const finalNumber = parseInt(number.textContent);
        const duration = 2000;
        const increment = finalNumber / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= finalNumber) {
                current = finalNumber;
                clearInterval(timer);
            }
            number.textContent = Math.floor(current) + (number.textContent.includes('+') ? '+' : '');
        }, 16);
    });
}

// Trigger number animation when stats section is visible
ScrollTrigger.create({
    trigger: '.about-stats',
    start: 'top 80%',
    onEnter: animateNumbers,
    once: true
});

// Skills bar animation
function animateSkillBars() {
    const skillBars = document.querySelectorAll('.skill-progress');

    skillBars.forEach(bar => {
        const width = bar.getAttribute('data-width');
        bar.style.width = width + '%';
    });
}

// Trigger skills animation when skills section is visible
ScrollTrigger.create({
    trigger: '.skills-section',
    start: 'top 80%',
    onEnter: animateSkillBars,
    once: true
});

console.log('Portfolio website loaded successfully!');
